exports.id=28,exports.ids=[28],exports.modules={1322:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:o,objectFit:s}=e,a=r?40*r:t,l=n?40*n:i,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},1933:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:r,width:n,quality:o}=e,s=o||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+s+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},9131:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(21122);let r=i(1322),n=i(27894),o=["-moz-initial","fill","none","scale-down",void 0];function s(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let u,d,c,{src:f,sizes:p,unoptimized:m=!1,priority:g=!1,loading:h,className:b,quality:v,width:y,height:_,fill:w=!1,style:x,overrideSrc:E,onLoad:j,onLoadingComplete:O,placeholder:S="empty",blurDataURL:C,fetchPriority:P,decoding:R="async",layout:M,objectFit:z,objectPosition:I,lazyBoundary:D,lazyRoot:A,...N}=e,{imgConf:k,showAltText:T,blurComplete:F,defaultLoader:G}=t,L=k||n.imageConfigDefault;if("allSizes"in L)u=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t),r=null==(i=L.qualities)?void 0:i.sort((e,t)=>e-t);u={...L,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=N.loader||G;delete N.loader,delete N.srcSet;let U="__next_img_default"in B;if(U){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:i,...r}=t;return e(r)}}if(M){"fill"===M&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let W="",q=a(y),X=a(_);if((l=f)&&"object"==typeof l&&(s(l)||void 0!==l.src)){let e=s(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,C=C||e.blurDataURL,W=e.src,!w)if(q||X){if(q&&!X){let t=q/e.width;X=Math.round(e.height*t)}else if(!q&&X){let t=X/e.height;q=Math.round(e.width*t)}}else q=e.width,X=e.height}let V=!g&&("lazy"===h||void 0===h);(!(f="string"==typeof f?f:W)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,V=!1),u.unoptimized&&(m=!0),U&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=a(v),J=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:I}:{},T?{}:{color:"transparent"},x),Y=F||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:q,heightInt:X,blurWidth:d,blurHeight:c,blurDataURL:C||"",objectFit:J.objectFit})+'")':'url("'+S+'")',$=o.includes(J.objectFit)?"fill"===J.objectFit?"100% 100%":"cover":J.objectFit,Z=Y?{backgroundSize:$,backgroundPosition:J.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},K=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:o,sizes:s,loader:a}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,s),d=l.length-1;return{sizes:s||"w"!==u?s:"100vw",srcSet:l.map((e,r)=>a({config:t,src:i,quality:o,width:e})+" "+("w"===u?e:r+1)+u).join(", "),src:a({config:t,src:i,quality:o,width:l[d]})}}({config:u,src:f,unoptimized:m,width:q,quality:H,sizes:p,loader:B});return{props:{...N,loading:V?"lazy":h,fetchPriority:P,width:q,height:X,decoding:R,className:b,style:{...J,...Z},sizes:K.sizes,srcSet:K.srcSet,src:E||K.src},meta:{unoptimized:m,priority:g,placeholder:S,fill:w}}}},12756:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},14959:(e,t,i)=>{"use strict";e.exports=i(94041).vendored.contexts.AmpContext},17903:(e,t,i)=>{"use strict";e.exports=i(94041).vendored.contexts.ImageConfigContext},21122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return i}});let i=e=>{}},27894:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{VALID_LOADERS:function(){return i},imageConfigDefault:function(){return r}});let i=["default","imgix","cloudinary","akamai","custom"],r={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},30512:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return g},defaultHead:function(){return c}});let r=i(14985),n=i(40740),o=i(60687),s=n._(i(43210)),a=r._(i(47755)),l=i(14959),u=i(89513),d=i(34604);function c(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}i(50148);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:i}=t;return e.reduce(f,[]).reverse().concat(c(i).reverse()).filter(function(){let e=new Set,t=new Set,i=new Set,r={};return n=>{let o=!0,s=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){s=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?o=!1:t.add(n.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(n.props.hasOwnProperty(t))if("charSet"===t)i.has(t)?o=!1:i.add(t);else{let e=n.props[t],i=r[t]||new Set;("name"!==t||!s)&&i.has(e)?o=!1:(i.add(e),r[t]=i)}}}return o}}()).reverse().map((e,t)=>{let r=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!i&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,s.default.cloneElement(e,t)}return s.default.cloneElement(e,{key:r})})}let g=function(e){let{children:t}=e,i=(0,s.useContext)(l.AmpStateContext),r=(0,s.useContext)(u.HeadManagerContext);return(0,o.jsx)(a.default,{reduceComponentsToState:m,headManager:r,inAmpMode:(0,d.isInAmpMode)(i),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32091:(e,t)=>{"use strict";function i(e){var t;let{config:i,src:r,width:n,quality:o}=e,s=o||(null==(t=i.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return i.path+"?url="+encodeURIComponent(r)+"&w="+n+"&q="+s+(r.startsWith("/_next/static/media/"),"")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return r}}),i.__next_img_default=!0;let r=i},34604:(e,t)=>{"use strict";function i(e){let{ampFirst:t=!1,hybrid:i=!1,hasQuery:r=!1}=void 0===e?{}:e;return t||i&&r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return i}})},41480:(e,t)=>{"use strict";function i(e){let{widthInt:t,heightInt:i,blurWidth:r,blurHeight:n,blurDataURL:o,objectFit:s}=e,a=r?40*r:t,l=n?40*n:i,u=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+u+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(u?"none":"contain"===s?"xMidYMid":"cover"===s?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+o+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return i}})},44953:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return l}}),i(50148);let r=i(41480),n=i(12756),o=["-moz-initial","fill","none","scale-down",void 0];function s(e){return void 0!==e.default}function a(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function l(e,t){var i,l;let u,d,c,{src:f,sizes:p,unoptimized:m=!1,priority:g=!1,loading:h,className:b,quality:v,width:y,height:_,fill:w=!1,style:x,overrideSrc:E,onLoad:j,onLoadingComplete:O,placeholder:S="empty",blurDataURL:C,fetchPriority:P,decoding:R="async",layout:M,objectFit:z,objectPosition:I,lazyBoundary:D,lazyRoot:A,...N}=e,{imgConf:k,showAltText:T,blurComplete:F,defaultLoader:G}=t,L=k||n.imageConfigDefault;if("allSizes"in L)u=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t),r=null==(i=L.qualities)?void 0:i.sort((e,t)=>e-t);u={...L,allSizes:e,deviceSizes:t,qualities:r}}if(void 0===G)throw Object.defineProperty(Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config"),"__NEXT_ERROR_CODE",{value:"E163",enumerable:!1,configurable:!0});let B=N.loader||G;delete N.loader,delete N.srcSet;let U="__next_img_default"in B;if(U){if("custom"===u.loader)throw Object.defineProperty(Error('Image with src "'+f+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader'),"__NEXT_ERROR_CODE",{value:"E252",enumerable:!1,configurable:!0})}else{let e=B;B=t=>{let{config:i,...r}=t;return e(r)}}if(M){"fill"===M&&(w=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[M];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[M];t&&!p&&(p=t)}let W="",q=a(y),X=a(_);if((l=f)&&"object"==typeof l&&(s(l)||void 0!==l.src)){let e=s(f)?f.default:f;if(!e.src)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E460",enumerable:!1,configurable:!0});if(!e.height||!e.width)throw Object.defineProperty(Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e)),"__NEXT_ERROR_CODE",{value:"E48",enumerable:!1,configurable:!0});if(d=e.blurWidth,c=e.blurHeight,C=C||e.blurDataURL,W=e.src,!w)if(q||X){if(q&&!X){let t=q/e.width;X=Math.round(e.height*t)}else if(!q&&X){let t=X/e.height;q=Math.round(e.width*t)}}else q=e.width,X=e.height}let V=!g&&("lazy"===h||void 0===h);(!(f="string"==typeof f?f:W)||f.startsWith("data:")||f.startsWith("blob:"))&&(m=!0,V=!1),u.unoptimized&&(m=!0),U&&!u.dangerouslyAllowSVG&&f.split("?",1)[0].endsWith(".svg")&&(m=!0);let H=a(v),J=Object.assign(w?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:z,objectPosition:I}:{},T?{}:{color:"transparent"},x),Y=F||"empty"===S?null:"blur"===S?'url("data:image/svg+xml;charset=utf-8,'+(0,r.getImageBlurSvg)({widthInt:q,heightInt:X,blurWidth:d,blurHeight:c,blurDataURL:C||"",objectFit:J.objectFit})+'")':'url("'+S+'")',$=o.includes(J.objectFit)?"fill"===J.objectFit?"100% 100%":"cover":J.objectFit,Z=Y?{backgroundSize:$,backgroundPosition:J.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:Y}:{},K=function(e){let{config:t,src:i,unoptimized:r,width:n,quality:o,sizes:s,loader:a}=e;if(r)return{src:i,srcSet:void 0,sizes:void 0};let{widths:l,kind:u}=function(e,t,i){let{deviceSizes:r,allSizes:n}=e;if(i){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let r;r=e.exec(i);)t.push(parseInt(r[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=r[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:r,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,s),d=l.length-1;return{sizes:s||"w"!==u?s:"100vw",srcSet:l.map((e,r)=>a({config:t,src:i,quality:o,width:e})+" "+("w"===u?e:r+1)+u).join(", "),src:a({config:t,src:i,quality:o,width:l[d]})}}({config:u,src:f,unoptimized:m,width:q,quality:H,sizes:p,loader:B});return{props:{...N,loading:V?"lazy":h,fetchPriority:P,width:q,height:X,decoding:R,className:b,style:{...J,...Z},sizes:K.sizes,srcSet:K.srcSet,src:E||K.src},meta:{unoptimized:m,priority:g,placeholder:S,fill:w}}}},46533:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return _}});let r=i(14985),n=i(40740),o=i(60687),s=n._(i(43210)),a=r._(i(51215)),l=r._(i(30512)),u=i(44953),d=i(12756),c=i(17903);i(50148);let f=i(69148),p=r._(i(1933)),m=i(53038),g={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function h(e,t,i,r,n,o,s){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==i?void 0:i.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let r=!1,n=!1;i.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>r,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{r=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==r?void 0:r.current)&&r.current(e)}}))}function b(e){return s.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,s.forwardRef)((e,t)=>{let{src:i,srcSet:r,sizes:n,height:a,width:l,decoding:u,className:d,style:c,fetchPriority:f,placeholder:p,loading:g,unoptimized:v,fill:y,onLoadRef:_,onLoadingCompleteRef:w,setBlurComplete:x,setShowAltText:E,sizesInput:j,onLoad:O,onError:S,...C}=e,P=(0,s.useCallback)(e=>{e&&(S&&(e.src=e.src),e.complete&&h(e,p,_,w,x,v,j))},[i,p,_,w,x,S,v,j]),R=(0,m.useMergedRef)(t,P);return(0,o.jsx)("img",{...C,...b(f),loading:g,width:l,height:a,decoding:u,"data-nimg":y?"fill":"1",className:d,style:c,sizes:n,srcSet:r,src:i,ref:R,onLoad:e=>{h(e.currentTarget,p,_,w,x,v,j)},onError:e=>{E(!0),"empty"!==p&&x(!0),S&&S(e)}})});function y(e){let{isAppRouter:t,imgAttributes:i}=e,r={as:"image",imageSrcSet:i.srcSet,imageSizes:i.sizes,crossOrigin:i.crossOrigin,referrerPolicy:i.referrerPolicy,...b(i.fetchPriority)};return t&&a.default.preload?(a.default.preload(i.src,r),null):(0,o.jsx)(l.default,{children:(0,o.jsx)("link",{rel:"preload",href:i.srcSet?void 0:i.src,...r},"__nimg-"+i.src+i.srcSet+i.sizes)})}let _=(0,s.forwardRef)((e,t)=>{let i=(0,s.useContext)(f.RouterContext),r=(0,s.useContext)(c.ImageConfigContext),n=(0,s.useMemo)(()=>{var e;let t=g||r||d.imageConfigDefault,i=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),o=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:i,deviceSizes:n,qualities:o}},[r]),{onLoad:a,onLoadingComplete:l}=e,m=(0,s.useRef)(a);(0,s.useEffect)(()=>{m.current=a},[a]);let h=(0,s.useRef)(l);(0,s.useEffect)(()=>{h.current=l},[l]);let[b,_]=(0,s.useState)(!1),[w,x]=(0,s.useState)(!1),{props:E,meta:j}=(0,u.getImgProps)(e,{defaultLoader:p.default,imgConf:n,blurComplete:b,showAltText:w});return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(v,{...E,unoptimized:j.unoptimized,placeholder:j.placeholder,fill:j.fill,onLoadRef:m,onLoadingCompleteRef:h,setBlurComplete:_,setShowAltText:x,sizesInput:e.sizes,ref:t}),j.priority?(0,o.jsx)(y,{isAppRouter:!i,imgAttributes:E}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},47755:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let r=i(43210),n=()=>{},o=()=>{};function s(e){var t;let{headManager:i,reduceComponentsToState:s}=e;function a(){if(i&&i.mountedInstances){let t=r.Children.toArray(Array.from(i.mountedInstances).filter(Boolean));i.updateHead(s(t,e))}}return null==i||null==(t=i.mountedInstances)||t.add(e.children),a(),n(()=>{var t;return null==i||null==(t=i.mountedInstances)||t.add(e.children),()=>{var t;null==i||null==(t=i.mountedInstances)||t.delete(e.children)}}),n(()=>(i&&(i._pendingUpdate=a),()=>{i&&(i._pendingUpdate=a)})),o(()=>(i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null),()=>{i&&i._pendingUpdate&&(i._pendingUpdate(),i._pendingUpdate=null)})),null}},49603:(e,t,i)=>{let{createProxy:r}=i(39844);e.exports=r("O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\image-component.js")},53384:(e,t,i)=>{"use strict";i.d(t,{default:()=>n.a});var r=i(70099),n=i.n(r)},69148:(e,t,i)=>{"use strict";e.exports=i(94041).vendored.contexts.RouterContext},70099:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var i in t)Object.defineProperty(e,i,{enumerable:!0,get:t[i]})}(t,{default:function(){return l},getImageProps:function(){return a}});let r=i(72639),n=i(9131),o=i(49603),s=r._(i(32091));function a(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:s.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,i]of Object.entries(t))void 0===i&&delete t[e];return{props:t}}let l=o.Image},89513:(e,t,i)=>{"use strict";e.exports=i(94041).vendored.contexts.HeadManagerContext}};