(()=>{var e={};e.id=453,e.ids=[453],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9540:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m,metadata:()=>c});var a=s(37413),r=s(34659),n=s(23916),i=s(25952),o=s(91466),l=s(76901);let c={title:"Dispensary Locations | Apothecary Extracts",description:"Find Apothecary Extracts dispensary locations in Boston and Cambridge, Massachusetts. Store hours, directions, and contact information.",keywords:"cannabis dispensary locations, Boston dispensary, Cambridge dispensary, Massachusetts marijuana store"},d=[{id:"downtown-boston",name:"Downtown Boston",address:{street:"123 Main Street",city:"Boston",state:"MA",zipCode:"02101"},phone:"(*************",email:"<EMAIL>",hours:{weekdays:"10:00 AM - 8:00 PM",saturday:"10:00 AM - 6:00 PM",sunday:"12:00 PM - 6:00 PM"},services:["Recreational","Medical","Delivery","Curbside"],amenities:["Parking Available","Wheelchair Accessible","Consultation Room","Express Pickup"],description:"Our flagship location in the heart of downtown Boston, featuring our full selection of premium cannabis products and expert consultation services.",image:"/images/locations/downtown-boston.jpg"},{id:"cambridge",name:"Cambridge",address:{street:"456 Harvard Avenue",city:"Cambridge",state:"MA",zipCode:"02138"},phone:"(*************",email:"<EMAIL>",hours:{weekdays:"10:00 AM - 8:00 PM",saturday:"10:00 AM - 6:00 PM",sunday:"12:00 PM - 6:00 PM"},services:["Recreational","Medical","Curbside"],amenities:["Street Parking","Wheelchair Accessible","Student Discounts","Educational Resources"],description:"Located near Harvard University, our Cambridge location offers a curated selection of products perfect for the academic community.",image:"/images/locations/cambridge.jpg"}];function m(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.l,{subtitle:"Our Locations",title:"Visit Apothecary Extracts",description:"Find a dispensary near you in the Greater Boston area. Each location offers our full selection of premium cannabis products with expert guidance.",backgroundImage:"/images/hero/locations-hero.jpg",primaryCTA:{text:"Shop Online",href:"/shop",icon:"cart"},secondaryCTA:{text:"Contact Us",href:"/contact",icon:"phone"}}),(0,a.jsx)(n.w,{padding:"xl",background:"white",children:(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent",children:"Our Dispensary Locations"}),(0,a.jsx)("p",{className:"text-lg text-neutral-charcoal/70 max-w-2xl mx-auto",children:"Two convenient locations serving the Greater Boston area with premium cannabis products and expert guidance."})]}),(0,a.jsx)("div",{className:"grid lg:grid-cols-2 gap-8",children:d.map(e=>(0,a.jsxs)(i.Zp,{className:"overflow-hidden",children:[(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-primary-sage to-primary-forest flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center text-neutral-white",children:[(0,a.jsx)(l.I,{name:"map-pin",size:"2xl",className:"mx-auto mb-2"}),(0,a.jsxs)("p",{className:"text-lg font-medium",children:[e.name," Location"]})]})}),(0,a.jsxs)(i.Wu,{className:"p-6",children:[(0,a.jsx)(i.aR,{className:"p-0 mb-4",children:(0,a.jsxs)(i.ZB,{className:"text-2xl font-accent",children:["Apothecary Extracts - ",e.name]})}),(0,a.jsx)("p",{className:"text-neutral-charcoal/70 mb-6",children:e.description}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(l.I,{name:"map-pin",size:"sm",className:"text-primary-sage"}),(0,a.jsxs)("span",{className:"text-neutral-charcoal",children:[e.address.street,", ",e.address.city,", ",e.address.state," ",e.address.zipCode]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(l.I,{name:"phone",size:"sm",className:"text-primary-sage"}),(0,a.jsx)("span",{className:"text-neutral-charcoal",children:e.phone})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(l.I,{name:"mail",size:"sm",className:"text-primary-sage"}),(0,a.jsx)("span",{className:"text-neutral-charcoal",children:e.email})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-charcoal mb-2",children:"Store Hours"}),(0,a.jsxs)("div",{className:"text-sm text-neutral-charcoal/70 space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Monday - Friday:"}),(0,a.jsx)("span",{children:e.hours.weekdays})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Saturday:"}),(0,a.jsx)("span",{children:e.hours.saturday})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:"Sunday:"}),(0,a.jsx)("span",{children:e.hours.sunday})]})]})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-charcoal mb-2",children:"Services"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:e.services.map(e=>(0,a.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium bg-primary-sage/10 text-primary-sage rounded-full",children:e},e))})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-semibold text-neutral-charcoal mb-2",children:"Amenities"}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2 text-sm text-neutral-charcoal/70",children:e.amenities.map(e=>(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.I,{name:"check",size:"sm",className:"text-primary-sage"}),(0,a.jsx)("span",{children:e})]},e))})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3",children:[(0,a.jsx)(o.Button,{variant:"primary",fullWidth:!0,children:"Get Directions"}),(0,a.jsx)(o.Button,{variant:"secondary",fullWidth:!0,children:"Call Store"})]})]})]},e.id))})]})}),(0,a.jsx)(n.w,{padding:"xl",background:"cream",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent",children:"What to Expect"}),(0,a.jsx)("p",{className:"text-lg text-neutral-charcoal/70",children:"Your first visit to Apothecary Extracts"})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,a.jsx)(i.Zp,{className:"p-6",children:(0,a.jsx)(i.Wu,{className:"p-0",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(l.I,{name:"user",size:"lg",className:"text-primary-sage"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-neutral-charcoal mb-2",children:"What to Bring"}),(0,a.jsxs)("ul",{className:"text-neutral-charcoal/70 space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"• Valid government-issued photo ID"}),(0,a.jsx)("li",{children:"• Medical marijuana card (if applicable)"}),(0,a.jsx)("li",{children:"• Cash or debit card for payment"}),(0,a.jsx)("li",{children:"• Questions about products or dosing"})]})]})]})})}),(0,a.jsx)(i.Zp,{className:"p-6",children:(0,a.jsx)(i.Wu,{className:"p-0",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(l.I,{name:"heart",size:"lg",className:"text-primary-sage"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-neutral-charcoal mb-2",children:"Our Service"}),(0,a.jsxs)("ul",{className:"text-neutral-charcoal/70 space-y-1 text-sm",children:[(0,a.jsx)("li",{children:"• Personalized product consultations"}),(0,a.jsx)("li",{children:"• Educational resources and guidance"}),(0,a.jsx)("li",{children:"• Professional, welcoming environment"}),(0,a.jsx)("li",{children:"• Respect for your privacy and needs"})]})]})]})})})]})]})}),(0,a.jsx)(n.w,{padding:"xl",background:"sage",className:"text-center",children:(0,a.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-white mb-6 font-accent",children:"Ready to Visit?"}),(0,a.jsx)("p",{className:"text-xl text-neutral-white/90 mb-8",children:"Stop by one of our locations or place an order online for pickup. Our knowledgeable staff is here to help you find the right products for your needs."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("a",{href:"/shop",className:"inline-flex items-center px-8 py-3 text-lg font-medium text-primary-sage bg-neutral-white rounded-md hover:bg-neutral-cream transition-colors duration-200",children:"Shop Online"}),(0,a.jsx)("a",{href:"/contact",className:"inline-flex items-center px-8 py-3 text-lg font-medium text-neutral-white border border-neutral-white rounded-md hover:bg-neutral-white hover:text-primary-sage transition-colors duration-200",children:"Contact Us"})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14676:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c={children:["",{children:["locations",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9540)),"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\locations\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,30173)),"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\locations\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/locations/page",pathname:"/locations",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20484:(e,t,s)=>{Promise.resolve().then(s.bind(s,79713)),Promise.resolve().then(s.bind(s,91466))},23916:(e,t,s)=>{"use strict";s.d(t,{w:()=>c});var a=s(37413);s(61120);var r=s(10974),n=s(87419);let i={white:"bg-neutral-white",cream:"bg-neutral-cream",stone:"bg-neutral-stone",sage:"bg-primary-sage text-neutral-white",image:"bg-cover bg-center bg-no-repeat relative"},o={none:"",sm:"py-8",md:"py-12",lg:"py-16",xl:"py-24"},l={light:"bg-black/20",medium:"bg-black/40",dark:"bg-black/60"},c=({background:e="white",backgroundImage:t,padding:s="md",containerMaxWidth:c="xl",overlay:d=!1,overlayOpacity:m="medium",className:x,children:h,style:p,...u})=>{let g={...p,..."image"===e&&t&&{backgroundImage:`url(${t})`}};return(0,a.jsxs)("section",{className:(0,r.cn)(i[e],o[s],x),style:g,...u,children:["image"===e&&d&&(0,a.jsx)("div",{className:(0,r.cn)("absolute inset-0",l[m])}),(0,a.jsx)(n.m,{maxWidth:c,className:"image"===e?"relative z-10":void 0,children:h})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34659:(e,t,s)=>{"use strict";s.d(t,{l:()=>a.Hero});var a=s(79713)},57284:(e,t,s)=>{Promise.resolve().then(s.bind(s,74290)),Promise.resolve().then(s.bind(s,5172))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(31658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74290:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Hero:()=>m,default:()=>x});var a=s(60687);s(43210);var r=s(4780),n=s(72422);let i={white:"bg-neutral-white",cream:"bg-neutral-cream",stone:"bg-neutral-stone",sage:"bg-primary-sage text-neutral-white",image:"bg-cover bg-center bg-no-repeat relative"},o={none:"",sm:"py-8",md:"py-12",lg:"py-16",xl:"py-24"},l={light:"bg-black/20",medium:"bg-black/40",dark:"bg-black/60"},c=({background:e="white",backgroundImage:t,padding:s="md",containerMaxWidth:c="xl",overlay:d=!1,overlayOpacity:m="medium",className:x,children:h,style:p,...u})=>{let g={...p,..."image"===e&&t&&{backgroundImage:`url(${t})`}};return(0,a.jsxs)("section",{className:(0,r.cn)(i[e],o[s],x),style:g,...u,children:["image"===e&&d&&(0,a.jsx)("div",{className:(0,r.cn)("absolute inset-0",l[m])}),(0,a.jsx)(n.m,{maxWidth:c,className:"image"===e?"relative z-10":void 0,children:h})]})};var d=s(43682);let m=({title:e,subtitle:t,description:s,backgroundImage:n,primaryCTA:i,secondaryCTA:o,overlay:l=!0,overlayOpacity:m="medium",textAlign:x="center",className:h})=>{let p=e=>{e?.onClick?e.onClick():e?.href&&(window.location.href=e.href)};return(0,a.jsx)(c,{background:n?"image":"sage",backgroundImage:n,overlay:l,overlayOpacity:m,padding:"xl",className:(0,r.cn)("min-h-[60vh] flex items-center",h),children:(0,a.jsxs)("div",{className:(0,r.cn)("w-full","center"===x&&"text-center","right"===x&&"text-right"),children:[t&&(0,a.jsx)("p",{className:"text-secondary-amber font-medium text-lg mb-4 tracking-wide uppercase",children:t}),(0,a.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-white mb-6 font-accent leading-tight",children:e}),s&&(0,a.jsx)("p",{className:"text-xl md:text-2xl text-neutral-white/90 mb-8 max-w-3xl mx-auto leading-relaxed",children:s}),(i||o)&&(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[i&&(0,a.jsx)(d.$,{variant:"accent",size:"lg",onClick:()=>p(i),leftIcon:i.icon,className:"min-w-[200px]",children:i.text}),o&&(0,a.jsx)(d.$,{variant:"ghost",size:"lg",onClick:()=>p(o),leftIcon:o.icon,className:"min-w-[200px] text-neutral-white border-neutral-white hover:bg-neutral-white hover:text-primary-sage",children:o.text})]})]})})},x=m},79551:e=>{"use strict";e.exports=require("url")},79713:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Hero:()=>r,default:()=>n});var a=s(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\Hero.tsx","Hero"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"O:\\\\VSCODE PROJECTS\\\\2025 NEW\\\\HeirloomCollective\\\\apothecary-extracts\\\\src\\\\components\\\\content\\\\Hero\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\Hero.tsx","default")},91466:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Button:()=>r,default:()=>n});var a=s(12907);let r=(0,a.registerClientReference)(function(){throw Error("Attempted to call Button() from the server but Button is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Button\\Button.tsx","Button"),n=(0,a.registerClientReference)(function(){throw Error("Attempted to call the default export of \"O:\\\\VSCODE PROJECTS\\\\2025 NEW\\\\HeirloomCollective\\\\apothecary-extracts\\\\src\\\\components\\\\ui\\\\Button\\\\Button.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Button\\Button.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,46,658,767],()=>s(14676));module.exports=a})();