# Page Layout Analysis

## Overview
This document provides a comprehensive breakdown of each page section from The Heirloom Collective website, including purpose, content strategy, design specifications, and implementation details.

## Global Layout Elements

### Header Component
**Purpose**: Primary navigation and brand identity
**Location**: Top of every page, sticky on scroll
**Content Strategy**: 
- Establish brand presence with logo
- Provide easy access to main sections
- Show location-specific information
- Display pre-order status for transparency

**Design Specifications**:
- Height: 80px desktop, 64px mobile
- Background: White with subtle shadow
- Logo: Left-aligned, links to homepage
- Navigation: Right-aligned, collapses to hamburger on mobile
- Pre-order status: Prominent display with color coding

**Implementation Details**:
```typescript
// Key features to implement
- Sticky positioning with scroll detection
- Mobile hamburger menu with slide-out animation
- Location selector with cart warning modal
- Search functionality with autocomplete
- Pre-order status with real-time updates
```

### Age Gate Modal
**Purpose**: Legal compliance for cannabis website
**Location**: First visit overlay
**Content Strategy**: 
- Clear age verification question
- Social media links for engagement
- Brand logo for recognition
- Compliance with state regulations

**Design Specifications**:
- Full-screen overlay with backdrop blur
- Centered modal with brand colors
- Large, clear buttons for verification
- Social media icons at bottom

### Footer Component
**Purpose**: Site navigation, legal compliance, and engagement
**Location**: Bottom of every page
**Content Strategy**:
- Comprehensive site navigation
- App download promotion
- Newsletter signup
- Social media engagement
- Legal compliance links

**Design Specifications**:
- Multi-column layout on desktop
- Stacked layout on mobile
- Dark background with light text
- Clear section divisions

## Homepage Layout Analysis

### Section 1: Hero Banner
**Purpose**: Primary brand message and call-to-action
**Content Strategy**: 
- Headline: "Craft-Quality Massachusetts Cannabis Dispensaries"
- Subtext: Brand positioning and quality emphasis
- Primary CTA: "Shop Now" - drives to product catalog
- Secondary CTA: "View Featured" - highlights special products

**Design Specifications**:
- Full-width background image with overlay
- Centered content with vertical alignment
- Large, bold typography for headline
- Contrasting button colors for CTAs

**Implementation Details**:
```typescript
// Hero component features
- Responsive background images (desktop/mobile)
- Parallax scrolling effect (optional)
- Animated text entrance
- Button hover animations
- Accessibility considerations for overlay text
```

### Section 2: Location Selector
**Purpose**: Help users find their preferred dispensary
**Content Strategy**:
- Clear location differentiation
- Service type indicators (Med/Rec)
- Visual representation with photos
- Direct links to location-specific shopping

**Design Specifications**:
- Two-column layout on desktop
- Card-based design with images
- Clear service badges
- Prominent action buttons

### Section 3: Product Categories Grid
**Purpose**: Product discovery and navigation
**Content Strategy**:
- Visual product category representation
- Clear category naming
- "Shop All" option for comprehensive browsing
- High-quality product imagery

**Design Specifications**:
- 6-column grid on desktop, 2-column on mobile
- Consistent card sizing and spacing
- Hover effects for interactivity
- Image optimization for fast loading

**Implementation Details**:
```typescript
// ProductGrid component features
- Responsive grid layout (CSS Grid or Flexbox)
- Image lazy loading
- Hover animations (scale, shadow)
- Keyboard navigation support
- Loading states for images
```

### Section 4: Brand Mission Statement
**Purpose**: Communicate brand values and differentiation
**Content Strategy**:
- Headline: "Where Technology Meets Passion in Craft Cannabis"
- Emphasis on quality, innovation, and tradition
- Two CTAs: "Our Cultivations" and "View Strains"
- Professional yet approachable tone

**Design Specifications**:
- Full-width section with background image
- Centered content with overlay
- Large typography for impact
- Contrasting button styles

### Section 5: Customer Testimonials
**Purpose**: Social proof and credibility building
**Content Strategy**:
- Real customer reviews from Google
- Location-specific attribution
- Variety of customer experiences
- Positive sentiment focus

**Design Specifications**:
- Carousel/slider layout
- Card-based testimonial design
- Customer name and source attribution
- Navigation dots or arrows

**Implementation Details**:
```typescript
// Testimonial carousel features
- Auto-play with pause on hover
- Touch/swipe support for mobile
- Keyboard navigation
- Smooth transitions
- Responsive card sizing
```

### Section 6: FAQ Section
**Purpose**: Address common customer questions
**Content Strategy**:
- "Visiting Our MA Dispensaries For The First Time?"
- Purchase limits and requirements
- First-time customer support
- Location-specific information
- Educational content about cannabis

**Design Specifications**:
- Accordion-style layout
- Clear question formatting
- Expandable answers with rich content
- Links to related resources

### Section 7: Brand Story
**Purpose**: Detailed brand positioning and values
**Content Strategy**:
- "The Heirloom Collective Is the Best Dispensary in Massachusetts"
- Emphasis on craft quality and community
- Location-specific content for SEO
- Educational approach to cannabis

**Design Specifications**:
- Long-form content with clear hierarchy
- Subheadings for scanability
- Embedded links to location pages
- Rich text formatting

### Section 8: Newsletter Signup
**Purpose**: Lead generation and customer retention
**Content Strategy**:
- "Sign up for our Newsletter"
- Member benefits emphasis
- Simple, clear value proposition

**Design Specifications**:
- Card-based layout with background image
- Prominent CTA button
- Minimal form fields (email only)

### Section 9: App Download Promotion
**Purpose**: Drive mobile app adoption
**Content Strategy**:
- "There's an app for that"
- Feature benefits (search, filtering)
- Platform availability (iOS/Android)

**Design Specifications**:
- Side-by-side layout with app store badges
- Feature highlights
- Visual app representation

## About Page Layout

### Section 1: Hero with Mission
**Purpose**: Brand story and mission statement
**Content Strategy**:
- "ABOUT THE HEIRLOOM COLLECTIVE"
- Quality and customer service focus
- Educational approach emphasis
- Community involvement messaging

**Design Specifications**:
- Large hero image (interior shot)
- Overlay text with brand colors
- Clear typography hierarchy

### Section 2: FAQ Accordion
**Purpose**: Detailed customer education
**Content Strategy**:
- Purchase limits and regulations
- Multi-dispensary visit information
- New customer assistance
- Location-specific details

**Design Specifications**:
- Expandable accordion sections
- Rich content with links
- Clear question formatting

### Section 3: Location Details
**Purpose**: Specific location information
**Content Strategy**:
- Detailed location descriptions
- Nearby attractions and amenities
- Local business recommendations
- Geographic context

**Design Specifications**:
- Two-column layout for locations
- Rich descriptive content
- Local business links

### Section 4: Mission Statement
**Purpose**: Core values and approach
**Content Strategy**:
- All-caps emphasis for impact
- Quality, transparency, and service focus
- Professional positioning

**Design Specifications**:
- Full-width section
- Large, bold typography
- Centered alignment

## Shop Page Layout

### Section 1: Location Selector
**Purpose**: Ensure users shop from correct location
**Content Strategy**:
- Clear location identification
- Cart transfer warning
- Service type indicators

**Design Specifications**:
- Modal overlay for location switching
- Clear warning messaging
- Prominent action buttons

## Responsive Design Considerations

### Mobile Optimizations
- Hamburger navigation menu
- Stacked layouts for content sections
- Touch-friendly button sizing
- Optimized image sizes
- Simplified navigation patterns

### Tablet Adaptations
- Adjusted grid columns (3-4 instead of 6)
- Maintained desktop-like navigation
- Optimized touch targets
- Balanced content density

### Desktop Enhancements
- Full navigation menu
- Multi-column layouts
- Hover effects and animations
- Larger content areas
- Enhanced visual hierarchy

## Content Strategy Principles

### Tone and Voice
- **Professional**: Establishing credibility in regulated industry
- **Educational**: Helping customers make informed decisions
- **Approachable**: Welcoming to all experience levels
- **Community-focused**: Emphasizing collective and local connection
- **Quality-oriented**: Highlighting craft and premium aspects

### SEO Considerations
- Location-based keywords throughout content
- Educational content for authority building
- Local business schema markup
- Internal linking strategy
- Meta descriptions and title optimization

### Accessibility Features
- Alt text for all images
- Proper heading hierarchy
- Keyboard navigation support
- Color contrast compliance
- Screen reader compatibility
- Focus indicators for interactive elements

## Performance Optimization

### Image Strategy
- WebP format with fallbacks
- Responsive image sizing
- Lazy loading implementation
- Optimized compression
- CDN delivery

### Loading Strategy
- Critical CSS inlining
- Progressive enhancement
- Code splitting by route
- Preloading key resources
- Optimized font loading

## Interactive Elements and Animations

### Scroll Behaviors
- **Header**: Sticky positioning with shadow on scroll
- **Hero**: Parallax background movement (subtle)
- **Cards**: Fade-in animation on scroll into view
- **Testimonials**: Auto-advancing carousel with smooth transitions

### Hover States
- **Buttons**: Color transition, slight scale, shadow increase
- **Product Cards**: Scale up (1.05x), shadow enhancement
- **Navigation Links**: Underline animation, color transition
- **Location Cards**: Subtle lift effect with shadow

### Modal Interactions
- **Age Gate**: Backdrop blur, fade-in animation
- **Location Switcher**: Slide-down from header, backdrop overlay
- **Mobile Menu**: Slide-in from right, staggered menu item animation

### Form Interactions
- **Input Focus**: Border color change, label animation
- **Button Loading**: Spinner animation, disabled state
- **Validation**: Error shake animation, color transitions

## Technical Implementation Notes

### State Management Requirements
- Age verification status (localStorage)
- Selected location (localStorage + URL params)
- Shopping cart state (per location)
- Mobile menu open/closed state
- Newsletter subscription status

### API Integration Points
- Newsletter subscription endpoint
- Contact form submission
- Location-based product inventory
- Pre-order status updates
- Customer testimonials (if dynamic)

### SEO Implementation
- Dynamic meta tags per page/location
- Structured data for local business
- Canonical URLs for location pages
- XML sitemap generation
- robots.txt configuration

### Analytics and Tracking
- Google Analytics 4 implementation
- Conversion tracking for newsletter signups
- User journey tracking through product categories
- Location-specific engagement metrics
- Performance monitoring (Core Web Vitals)

This comprehensive analysis provides the foundation for implementing each page section with proper attention to user experience, brand consistency, and technical performance.
