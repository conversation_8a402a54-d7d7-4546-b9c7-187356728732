import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { STORAGE_KEYS, LEGAL_AGE } from '@/lib/constants';
import { calculateAge } from '@/lib/utils';

interface AgeVerificationState {
  isVerified: boolean;
  dateOfBirth: string | null;
  verificationDate: string | null;
  
  // Actions
  verifyAge: (dateOfBirth: string) => boolean;
  setVerified: (verified: boolean) => void;
  reset: () => void;
  isLegalAge: () => boolean;
}

export const useAgeVerificationStore = create<AgeVerificationState>()(
  persist(
    (set, get) => ({
      isVerified: false,
      dateOfBirth: null,
      verificationDate: null,

      verifyAge: (dateOfBirth: string) => {
        const age = calculateAge(dateOfBirth);
        const isLegal = age >= LEGAL_AGE.recreational;
        
        if (isLegal) {
          set({
            isVerified: true,
            dateOfBirth,
            verificationDate: new Date().toISOString(),
          });
        }
        
        return isLegal;
      },

      setVerified: (verified: boolean) => {
        set({ isVerified: verified });
      },

      reset: () => {
        set({
          isVerified: false,
          dateOfBirth: null,
          verificationDate: null,
        });
      },

      isLegalAge: () => {
        const { dateOfBirth } = get();
        if (!dateOfBirth) return false;
        
        const age = calculateAge(dateOfBirth);
        return age >= LEGAL_AGE.recreational;
      },
    }),
    {
      name: STORAGE_KEYS.ageVerified,
      partialize: (state) => ({
        isVerified: state.isVerified,
        dateOfBirth: state.dateOfBirth,
        verificationDate: state.verificationDate,
      }),
    }
  )
);
