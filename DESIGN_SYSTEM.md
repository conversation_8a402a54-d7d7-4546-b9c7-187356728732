# The Heirloom Collective - Design System

## Overview
This design system defines the visual language, components, and patterns for recreating The Heirloom Collective website. It emphasizes craft quality, community connection, and professional cannabis retail experience.

## Brand Identity

### Brand Values
- **Craft Quality**: Premium, artisanal approach to cannabis
- **Community**: Collective spirit and local connection
- **Education**: Informative and approachable expertise
- **Trust**: Professional, compliant, and reliable
- **Accessibility**: Welcoming to all experience levels

### Tone of Voice
- Professional yet approachable
- Educational without being condescending
- Community-focused and inclusive
- Confident in expertise
- Warm and welcoming

## Color Palette

### Primary Colors
```css
--color-primary-green: #2D5016;      /* Deep forest green - main brand color */
--color-primary-light: #4A7C2A;      /* Lighter green for hover states */
--color-primary-dark: #1A3009;       /* Darker green for emphasis */
```

### Secondary Colors
```css
--color-secondary-gold: #D4AF37;     /* Gold accent for premium feel */
--color-secondary-cream: #F5F5DC;    /* Warm cream for backgrounds */
--color-secondary-earth: #8B4513;    /* Earth brown for natural elements */
```

### Neutral Colors
```css
--color-neutral-white: #FFFFFF;      /* Pure white */
--color-neutral-light: #F8F9FA;      /* Light gray backgrounds */
--color-neutral-medium: #6C757D;     /* Medium gray for text */
--color-neutral-dark: #212529;       /* Dark gray for headings */
--color-neutral-black: #000000;      /* Pure black */
```

### Status Colors
```css
--color-success: #28A745;            /* Success states */
--color-warning: #FFC107;            /* Warning states */
--color-error: #DC3545;              /* Error states */
--color-info: #17A2B8;               /* Information states */
```

## Typography

### Font Families
```css
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
--font-secondary: 'Playfair Display', Georgia, serif;
--font-mono: 'JetBrains Mono', 'Courier New', monospace;
```

### Font Weights
```css
--font-weight-light: 300;
--font-weight-regular: 400;
--font-weight-medium: 500;
--font-weight-semibold: 600;
--font-weight-bold: 700;
--font-weight-black: 900;
```

### Font Sizes
```css
--font-size-xs: 0.75rem;     /* 12px */
--font-size-sm: 0.875rem;    /* 14px */
--font-size-base: 1rem;      /* 16px */
--font-size-lg: 1.125rem;    /* 18px */
--font-size-xl: 1.25rem;     /* 20px */
--font-size-2xl: 1.5rem;     /* 24px */
--font-size-3xl: 1.875rem;   /* 30px */
--font-size-4xl: 2.25rem;    /* 36px */
--font-size-5xl: 3rem;       /* 48px */
--font-size-6xl: 3.75rem;    /* 60px */
```

### Line Heights
```css
--line-height-tight: 1.25;
--line-height-normal: 1.5;
--line-height-relaxed: 1.75;
```

## Spacing System

### Base Unit
```css
--spacing-unit: 0.25rem; /* 4px base unit */
```

### Spacing Scale
```css
--spacing-1: 0.25rem;    /* 4px */
--spacing-2: 0.5rem;     /* 8px */
--spacing-3: 0.75rem;    /* 12px */
--spacing-4: 1rem;       /* 16px */
--spacing-5: 1.25rem;    /* 20px */
--spacing-6: 1.5rem;     /* 24px */
--spacing-8: 2rem;       /* 32px */
--spacing-10: 2.5rem;    /* 40px */
--spacing-12: 3rem;      /* 48px */
--spacing-16: 4rem;      /* 64px */
--spacing-20: 5rem;      /* 80px */
--spacing-24: 6rem;      /* 96px */
--spacing-32: 8rem;      /* 128px */
```

## Breakpoints

### Screen Sizes
```css
--breakpoint-xs: 320px;   /* Extra small devices */
--breakpoint-sm: 640px;   /* Small devices */
--breakpoint-md: 768px;   /* Medium devices */
--breakpoint-lg: 1024px;  /* Large devices */
--breakpoint-xl: 1280px;  /* Extra large devices */
--breakpoint-2xl: 1536px; /* 2X large devices */
```

### Container Widths
```css
--container-sm: 640px;
--container-md: 768px;
--container-lg: 1024px;
--container-xl: 1280px;
--container-2xl: 1536px;
```

## Shadows

### Box Shadows
```css
--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
--shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
```

## Border Radius

### Radius Scale
```css
--radius-none: 0;
--radius-sm: 0.125rem;    /* 2px */
--radius-base: 0.25rem;   /* 4px */
--radius-md: 0.375rem;    /* 6px */
--radius-lg: 0.5rem;      /* 8px */
--radius-xl: 0.75rem;     /* 12px */
--radius-2xl: 1rem;       /* 16px */
--radius-full: 9999px;    /* Full circle */
```

## Z-Index Scale

### Layer Hierarchy
```css
--z-index-dropdown: 1000;
--z-index-sticky: 1020;
--z-index-fixed: 1030;
--z-index-modal-backdrop: 1040;
--z-index-modal: 1050;
--z-index-popover: 1060;
--z-index-tooltip: 1070;
--z-index-toast: 1080;
```

## Animation & Transitions

### Timing Functions
```css
--ease-linear: linear;
--ease-in: cubic-bezier(0.4, 0, 1, 1);
--ease-out: cubic-bezier(0, 0, 0.2, 1);
--ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
```

### Duration Scale
```css
--duration-75: 75ms;
--duration-100: 100ms;
--duration-150: 150ms;
--duration-200: 200ms;
--duration-300: 300ms;
--duration-500: 500ms;
--duration-700: 700ms;
--duration-1000: 1000ms;
```

## Component Specifications

### Buttons
- **Primary**: Green background, white text, medium padding
- **Secondary**: White background, green border, green text
- **Ghost**: Transparent background, green text
- **Sizes**: Small (32px), Medium (40px), Large (48px)
- **States**: Default, Hover, Active, Disabled, Loading

### Cards
- **Background**: White with subtle shadow
- **Border**: None or 1px light gray
- **Radius**: Medium (6px)
- **Padding**: 24px
- **Hover**: Slight shadow increase and transform

### Forms
- **Input Height**: 48px
- **Border**: 1px solid light gray
- **Focus**: Green border with subtle glow
- **Error**: Red border with error message
- **Label**: Medium weight, dark gray

### Navigation
- **Height**: 80px desktop, 64px mobile
- **Background**: White with shadow
- **Logo**: Left aligned
- **Menu**: Right aligned, horizontal on desktop, hamburger on mobile

## Accessibility Guidelines

### Color Contrast
- All text must meet WCAG AA standards (4.5:1 ratio)
- Interactive elements must meet WCAG AA standards
- Focus indicators must be clearly visible

### Typography
- Minimum font size: 16px for body text
- Line height: Minimum 1.5 for body text
- Adequate spacing between interactive elements (44px minimum)

### Interactive Elements
- All interactive elements must be keyboard accessible
- Focus indicators must be visible and consistent
- Touch targets must be at least 44px x 44px

## Usage Guidelines

### Do's
- Use consistent spacing from the scale
- Maintain proper color contrast ratios
- Follow the component hierarchy
- Use semantic HTML elements
- Implement proper focus management

### Don'ts
- Don't create custom spacing values outside the scale
- Don't use colors that fail accessibility standards
- Don't break the visual hierarchy
- Don't forget mobile-first responsive design
- Don't ignore keyboard navigation

## Implementation Notes

### CSS Custom Properties
All design tokens should be implemented as CSS custom properties for easy theming and maintenance.

### Component Library
Build components using a consistent API with proper TypeScript interfaces and comprehensive documentation.

### Testing
Ensure all components are tested for accessibility, responsive behavior, and cross-browser compatibility.
