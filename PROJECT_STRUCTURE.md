# React Project Structure

## Overview
This document defines the complete file and folder structure for recreating The Heirloom Collective website using React, TypeScript, and modern web development practices.

## Technology Stack

### Core Technologies
- **React 18+**: UI library with hooks and concurrent features
- **TypeScript**: Type safety and developer experience
- **Next.js 14+**: Full-stack React framework with App Router
- **Tailwind CSS**: Utility-first CSS framework
- **Framer Motion**: Animation library
- **React Hook Form**: Form handling
- **Zustand**: State management
- **React Query**: Server state management

### Development Tools
- **ESLint**: Code linting
- **Prettier**: Code formatting
- **Husky**: Git hooks
- **Jest**: Unit testing
- **Playwright**: E2E testing
- **Storybook**: Component documentation

## Project Root Structure

```
heirloom-collective/
├── .env.local                    # Environment variables
├── .env.example                  # Environment variables template
├── .eslintrc.json               # ESLint configuration
├── .gitignore                   # Git ignore rules
├── .prettierrc                  # Prettier configuration
├── README.md                    # Project documentation
├── next.config.js               # Next.js configuration
├── package.json                 # Dependencies and scripts
├── tailwind.config.js           # Tailwind CSS configuration
├── tsconfig.json                # TypeScript configuration
├── playwright.config.ts         # Playwright configuration
├── jest.config.js               # Jest configuration
├── DESIGN_SYSTEM.md             # Design system documentation
├── COMPONENT_ARCHITECTURE.md    # Component architecture
├── PROJECT_STRUCTURE.md         # This file
├── public/                      # Static assets
├── src/                         # Source code
├── docs/                        # Documentation
├── tests/                       # Test files
└── .storybook/                  # Storybook configuration
```

## Source Code Structure (`src/`)

```
src/
├── app/                         # Next.js App Router
│   ├── globals.css             # Global styles
│   ├── layout.tsx              # Root layout
│   ├── page.tsx                # Home page
│   ├── loading.tsx             # Loading UI
│   ├── error.tsx               # Error UI
│   ├── not-found.tsx           # 404 page
│   ├── about/                  # About pages
│   │   ├── page.tsx
│   │   └── layout.tsx
│   ├── shop/                   # Shop pages
│   │   ├── page.tsx
│   │   ├── layout.tsx
│   │   ├── [category]/
│   │   │   └── page.tsx
│   │   └── product/
│   │       └── [id]/
│   │           └── page.tsx
│   ├── locations/              # Location pages
│   │   ├── page.tsx
│   │   └── [location]/
│   │       └── page.tsx
│   ├── blog/                   # Blog pages
│   │   ├── page.tsx
│   │   └── [slug]/
│   │       └── page.tsx
│   ├── events/                 # Events pages
│   │   ├── page.tsx
│   │   └── [id]/
│   │       └── page.tsx
│   ├── newsletter/             # Newsletter page
│   │   └── page.tsx
│   ├── contact/                # Contact page
│   │   └── page.tsx
│   ├── careers/                # Careers page
│   │   └── page.tsx
│   └── api/                    # API routes
│       ├── newsletter/
│       │   └── route.ts
│       ├── contact/
│       │   └── route.ts
│       └── locations/
│           └── route.ts
├── components/                  # React components
│   ├── layout/                 # Layout components
│   │   ├── Header/
│   │   │   ├── index.tsx
│   │   │   ├── Header.tsx
│   │   │   ├── Header.stories.tsx
│   │   │   └── Header.test.tsx
│   │   ├── Footer/
│   │   │   ├── index.tsx
│   │   │   ├── Footer.tsx
│   │   │   ├── Footer.stories.tsx
│   │   │   └── Footer.test.tsx
│   │   ├── Navigation/
│   │   │   ├── index.tsx
│   │   │   ├── NavigationMenu.tsx
│   │   │   ├── MobileMenu.tsx
│   │   │   └── LocationSelector.tsx
│   │   ├── Container/
│   │   │   ├── index.tsx
│   │   │   └── Container.tsx
│   │   └── Section/
│   │       ├── index.tsx
│   │       └── Section.tsx
│   ├── ui/                     # Basic UI components
│   │   ├── Button/
│   │   │   ├── index.tsx
│   │   │   ├── Button.tsx
│   │   │   ├── Button.stories.tsx
│   │   │   └── Button.test.tsx
│   │   ├── Card/
│   │   │   ├── index.tsx
│   │   │   ├── Card.tsx
│   │   │   ├── Card.stories.tsx
│   │   │   └── Card.test.tsx
│   │   ├── Modal/
│   │   │   ├── index.tsx
│   │   │   ├── Modal.tsx
│   │   │   ├── AgeGate.tsx
│   │   │   └── Modal.stories.tsx
│   │   ├── Icon/
│   │   │   ├── index.tsx
│   │   │   ├── Icon.tsx
│   │   │   └── icons/
│   │   │       ├── index.ts
│   │   │       ├── ChevronDown.tsx
│   │   │       ├── Menu.tsx
│   │   │       ├── Close.tsx
│   │   │       └── Search.tsx
│   │   ├── Badge/
│   │   │   ├── index.tsx
│   │   │   └── Badge.tsx
│   │   ├── Spinner/
│   │   │   ├── index.tsx
│   │   │   └── Spinner.tsx
│   │   └── Image/
│   │       ├── index.tsx
│   │       └── Image.tsx
│   ├── forms/                  # Form components
│   │   ├── Input/
│   │   │   ├── index.tsx
│   │   │   ├── Input.tsx
│   │   │   └── Input.stories.tsx
│   │   ├── Select/
│   │   │   ├── index.tsx
│   │   │   └── Select.tsx
│   │   ├── SearchBar/
│   │   │   ├── index.tsx
│   │   │   └── SearchBar.tsx
│   │   └── NewsletterForm/
│   │       ├── index.tsx
│   │       └── NewsletterForm.tsx
│   ├── content/                # Content components
│   │   ├── Hero/
│   │   │   ├── index.tsx
│   │   │   ├── Hero.tsx
│   │   │   └── Hero.stories.tsx
│   │   ├── ProductGrid/
│   │   │   ├── index.tsx
│   │   │   ├── ProductGrid.tsx
│   │   │   └── ProductCard.tsx
│   │   ├── LocationCard/
│   │   │   ├── index.tsx
│   │   │   └── LocationCard.tsx
│   │   ├── Testimonials/
│   │   │   ├── index.tsx
│   │   │   ├── TestimonialCard.tsx
│   │   │   └── TestimonialCarousel.tsx
│   │   ├── FAQ/
│   │   │   ├── index.tsx
│   │   │   ├── FAQ.tsx
│   │   │   └── Accordion.tsx
│   │   └── Mission/
│   │       ├── index.tsx
│   │       └── Mission.tsx
│   └── specialized/            # Business-specific components
│       ├── AgeVerification/
│       │   ├── index.tsx
│       │   └── AgeGate.tsx
│       ├── AppDownload/
│       │   ├── index.tsx
│       │   └── AppDownload.tsx
│       ├── SocialLinks/
│       │   ├── index.tsx
│       │   └── SocialLinks.tsx
│       └── PreOrderStatus/
│           ├── index.tsx
│           └── PreOrderStatus.tsx
├── hooks/                      # Custom React hooks
│   ├── useLocalStorage.ts
│   ├── useAgeVerification.ts
│   ├── useLocation.ts
│   ├── useCart.ts
│   └── useNewsletter.ts
├── lib/                        # Utility libraries
│   ├── utils.ts               # General utilities
│   ├── constants.ts           # App constants
│   ├── validations.ts         # Form validations
│   ├── api.ts                 # API client
│   └── types.ts               # TypeScript types
├── store/                      # State management
│   ├── index.ts               # Store configuration
│   ├── ageVerificationStore.ts
│   ├── locationStore.ts
│   ├── cartStore.ts
│   └── uiStore.ts
├── styles/                     # Styling
│   ├── globals.css            # Global styles
│   ├── components.css         # Component styles
│   └── utilities.css          # Utility classes
└── data/                       # Static data
    ├── navigation.ts          # Navigation structure
    ├── products.ts            # Product categories
    ├── locations.ts           # Store locations
    ├── testimonials.ts        # Customer testimonials
    └── faq.ts                 # FAQ content
```

## Public Assets Structure (`public/`)

```
public/
├── favicon.ico                 # Favicon
├── robots.txt                  # SEO robots file
├── sitemap.xml                 # SEO sitemap
├── manifest.json               # PWA manifest
├── images/                     # Image assets
│   ├── logo/
│   │   ├── logo.svg
│   │   ├── logo-white.svg
│   │   └── logo-icon.svg
│   ├── hero/
│   │   ├── hero-bg.jpg
│   │   └── hero-bg-mobile.jpg
│   ├── products/
│   │   ├── flower.png
│   │   ├── pre-rolls.png
│   │   ├── edibles.png
│   │   ├── concentrates.png
│   │   ├── tinctures.png
│   │   └── vapes.png
│   ├── locations/
│   │   ├── hadley-exterior.jpg
│   │   ├── hadley-interior.jpg
│   │   ├── bernardston-exterior.jpg
│   │   └── bernardston-interior.jpg
│   ├── app/
│   │   ├── app-store.png
│   │   └── google-play.png
│   └── icons/
│       ├── facebook.svg
│       ├── instagram.svg
│       └── youtube.svg
└── videos/                     # Video assets
    └── hero-video.mp4
```

## Documentation Structure (`docs/`)

```
docs/
├── README.md                   # Documentation overview
├── SETUP.md                    # Setup instructions
├── DEPLOYMENT.md               # Deployment guide
├── API.md                      # API documentation
├── COMPONENTS.md               # Component usage guide
├── STYLING.md                  # Styling guidelines
└── TESTING.md                  # Testing guidelines
```

## Testing Structure (`tests/`)

```
tests/
├── __mocks__/                  # Test mocks
├── e2e/                        # End-to-end tests
│   ├── home.spec.ts
│   ├── navigation.spec.ts
│   ├── shop.spec.ts
│   └── age-verification.spec.ts
├── integration/                # Integration tests
│   ├── newsletter.test.ts
│   └── contact-form.test.ts
└── utils/                      # Test utilities
    ├── test-utils.tsx
    └── mock-data.ts
```

## Configuration Files

### `package.json` Scripts
```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "lint:fix": "next lint --fix",
    "type-check": "tsc --noEmit",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:e2e": "playwright test",
    "storybook": "storybook dev -p 6006",
    "build-storybook": "storybook build"
  }
}
```

### Environment Variables (`.env.local`)
```
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEWSLETTER_API_KEY=your_newsletter_api_key
CONTACT_EMAIL=<EMAIL>
```

## Development Workflow

### Component Development
1. Create component in appropriate directory
2. Add TypeScript interfaces
3. Write Storybook stories
4. Add unit tests
5. Update documentation

### Page Development
1. Create page in `app/` directory
2. Add metadata and SEO
3. Implement responsive design
4. Add E2E tests
5. Test accessibility

### Feature Development
1. Plan component architecture
2. Implement components bottom-up
3. Add state management if needed
4. Write comprehensive tests
5. Document usage patterns

## Build and Deployment

### Build Process
- TypeScript compilation
- CSS optimization with Tailwind
- Image optimization
- Bundle analysis
- Performance auditing

### Deployment Targets
- **Vercel**: Recommended for Next.js
- **Netlify**: Alternative static hosting
- **AWS**: Enterprise deployment
- **Docker**: Containerized deployment

## Performance Considerations

### Code Splitting
- Automatic route-based splitting
- Dynamic imports for heavy components
- Lazy loading for images and videos

### Optimization
- Image optimization with Next.js Image
- Font optimization
- Bundle size monitoring
- Core Web Vitals tracking

This structure provides a solid foundation for building a scalable, maintainable React application that recreates The Heirloom Collective website with modern best practices.
