(()=>{var e={};e.id=220,e.ids=[220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23916:(e,t,a)=>{"use strict";a.d(t,{w:()=>o});var r=a(37413);a(61120);var s=a(10974),i=a(87419);let n={white:"bg-neutral-white",cream:"bg-neutral-cream",stone:"bg-neutral-stone",sage:"bg-primary-sage text-neutral-white",image:"bg-cover bg-center bg-no-repeat relative"},l={none:"",sm:"py-8",md:"py-12",lg:"py-16",xl:"py-24"},c={light:"bg-black/20",medium:"bg-black/40",dark:"bg-black/60"},o=({background:e="white",backgroundImage:t,padding:a="md",containerMaxWidth:o="xl",overlay:d=!1,overlayOpacity:m="medium",className:x,children:h,style:u,...p})=>{let g={...u,..."image"===e&&t&&{backgroundImage:`url(${t})`}};return(0,r.jsxs)("section",{className:(0,s.cn)(n[e],l[a],x),style:g,...p,children:["image"===e&&d&&(0,r.jsx)("div",{className:(0,s.cn)("absolute inset-0",c[m])}),(0,r.jsx)(i.m,{maxWidth:o,className:"image"===e?"relative z-10":void 0,children:h})]})}},28770:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o,metadata:()=>c});var r=a(37413),s=a(34659),i=a(23916),n=a(25952),l=a(76901);let c={title:"About Us | Apothecary Extracts",description:"Learn about Apothecary Extracts - where traditional apothecary meets modern cannabis science. Our mission, values, and commitment to premium medicinal cannabis.",keywords:"about apothecary extracts, cannabis dispensary story, medicinal cannabis, Boston dispensary, Cambridge dispensary"};function o(){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.l,{subtitle:"Our Story",title:"Where Traditional Apothecary Meets Modern Cannabis Science",description:"Founded on the principles of botanical medicine and scientific precision, Apothecary Extracts bridges ancient healing wisdom with contemporary cannabis innovation.",backgroundImage:"/images/hero/about-hero.jpg",primaryCTA:{text:"Visit Our Locations",href:"/locations",icon:"map-pin"},secondaryCTA:{text:"Shop Products",href:"/shop",icon:"leaf"}}),(0,r.jsx)(i.w,{padding:"xl",background:"white",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-charcoal mb-6 font-accent",children:"Our Mission"}),(0,r.jsx)("p",{className:"text-xl text-neutral-charcoal/80 leading-relaxed mb-8",children:"To provide premium cannabis products that honor the ancient tradition of botanical medicine while embracing modern scientific understanding. We believe in the healing power of nature and are committed to supporting your wellness journey with respect, care, and expertise."}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-8 mt-12",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary-sage/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(l.I,{name:"leaf",size:"xl",className:"text-primary-sage"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-charcoal mb-2",children:"Natural Healing"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70 text-sm",children:"Honoring the wisdom of traditional botanical medicine"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary-sage/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(l.I,{name:"beaker",size:"xl",className:"text-primary-sage"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-charcoal mb-2",children:"Scientific Precision"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70 text-sm",children:"Modern testing and quality assurance standards"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary-sage/10 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)(l.I,{name:"heart",size:"xl",className:"text-primary-sage"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-charcoal mb-2",children:"Compassionate Care"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70 text-sm",children:"Personalized guidance for your wellness journey"})]})]})]})}),(0,r.jsx)(i.w,{padding:"xl",background:"cream",children:(0,r.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent",children:"Our Values"}),(0,r.jsx)("p",{className:"text-lg text-neutral-charcoal/70 max-w-2xl mx-auto",children:"These core principles guide everything we do, from product selection to customer service."})]}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsx)(n.Wu,{className:"p-0",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(l.I,{name:"shield",size:"lg",className:"text-primary-sage"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-charcoal mb-2",children:"Quality & Purity"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70",children:"Every product undergoes rigorous testing for potency, purity, and safety. We partner only with licensed cultivators who share our commitment to excellence."})]})]})})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsx)(n.Wu,{className:"p-0",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(l.I,{name:"user",size:"lg",className:"text-primary-sage"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-charcoal mb-2",children:"Patient-Centered Care"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70",children:"Your wellness journey is unique. Our knowledgeable staff provides personalized consultations to help you find the right products for your specific needs."})]})]})})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsx)(n.Wu,{className:"p-0",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(l.I,{name:"award",size:"lg",className:"text-primary-sage"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-charcoal mb-2",children:"Education & Transparency"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70",children:"We believe in empowering our customers with knowledge. From dosing guidance to strain information, we provide the education you need to make informed decisions."})]})]})})}),(0,r.jsx)(n.Zp,{className:"p-6",children:(0,r.jsx)(n.Wu,{className:"p-0",children:(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(l.I,{name:"leaf",size:"lg",className:"text-primary-sage"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-neutral-charcoal mb-2",children:"Sustainability"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70",children:"We're committed to environmentally responsible practices, from sustainable packaging to supporting eco-conscious cultivation methods."})]})]})})})]})]})}),(0,r.jsx)(i.w,{padding:"xl",background:"white",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-charcoal mb-6 font-accent",children:"Our Team"}),(0,r.jsx)("p",{className:"text-lg text-neutral-charcoal/70 mb-12",children:"Our experienced team combines expertise in cannabis science, botanical medicine, and customer care to provide you with the best possible experience."}),(0,r.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-primary-sage to-primary-forest rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,r.jsx)(l.I,{name:"user",size:"xl",className:"text-neutral-white"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-charcoal mb-1",children:"Licensed Pharmacists"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70 text-sm",children:"Bringing pharmaceutical expertise to cannabis medicine"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-primary-sage to-primary-forest rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,r.jsx)(l.I,{name:"beaker",size:"xl",className:"text-neutral-white"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-charcoal mb-1",children:"Cannabis Scientists"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70 text-sm",children:"Research-driven approach to product selection and quality"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-primary-sage to-primary-forest rounded-full mx-auto mb-4 flex items-center justify-center",children:(0,r.jsx)(l.I,{name:"heart",size:"xl",className:"text-neutral-white"})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-neutral-charcoal mb-1",children:"Patient Advocates"}),(0,r.jsx)("p",{className:"text-neutral-charcoal/70 text-sm",children:"Dedicated to supporting your wellness journey with compassion"})]})]})]})}),(0,r.jsx)(i.w,{padding:"xl",background:"sage",className:"text-center",children:(0,r.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-neutral-white mb-6 font-accent",children:"Experience the Apothecary Difference"}),(0,r.jsx)("p",{className:"text-xl text-neutral-white/90 mb-8",children:"Visit one of our locations to discover how traditional botanical wisdom and modern cannabis science can support your wellness journey."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsx)("a",{href:"/locations",className:"inline-flex items-center px-8 py-3 text-lg font-medium text-primary-sage bg-neutral-white rounded-md hover:bg-neutral-cream transition-colors duration-200",children:"Find a Location"}),(0,r.jsx)("a",{href:"/contact",className:"inline-flex items-center px-8 py-3 text-lg font-medium text-neutral-white border border-neutral-white rounded-md hover:bg-neutral-white hover:text-primary-sage transition-colors duration-200",children:"Contact Us"})]})]})})]})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32554:(e,t,a)=>{Promise.resolve().then(a.bind(a,79713))},33873:e=>{"use strict";e.exports=require("path")},34659:(e,t,a)=>{"use strict";a.d(t,{l:()=>r.Hero});var r=a(79713)},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74290:(e,t,a)=>{"use strict";a.r(t),a.d(t,{Hero:()=>m,default:()=>x});var r=a(60687);a(43210);var s=a(4780),i=a(72422);let n={white:"bg-neutral-white",cream:"bg-neutral-cream",stone:"bg-neutral-stone",sage:"bg-primary-sage text-neutral-white",image:"bg-cover bg-center bg-no-repeat relative"},l={none:"",sm:"py-8",md:"py-12",lg:"py-16",xl:"py-24"},c={light:"bg-black/20",medium:"bg-black/40",dark:"bg-black/60"},o=({background:e="white",backgroundImage:t,padding:a="md",containerMaxWidth:o="xl",overlay:d=!1,overlayOpacity:m="medium",className:x,children:h,style:u,...p})=>{let g={...u,..."image"===e&&t&&{backgroundImage:`url(${t})`}};return(0,r.jsxs)("section",{className:(0,s.cn)(n[e],l[a],x),style:g,...p,children:["image"===e&&d&&(0,r.jsx)("div",{className:(0,s.cn)("absolute inset-0",c[m])}),(0,r.jsx)(i.m,{maxWidth:o,className:"image"===e?"relative z-10":void 0,children:h})]})};var d=a(43682);let m=({title:e,subtitle:t,description:a,backgroundImage:i,primaryCTA:n,secondaryCTA:l,overlay:c=!0,overlayOpacity:m="medium",textAlign:x="center",className:h})=>{let u=e=>{e?.onClick?e.onClick():e?.href&&(window.location.href=e.href)};return(0,r.jsx)(o,{background:i?"image":"sage",backgroundImage:i,overlay:c,overlayOpacity:m,padding:"xl",className:(0,s.cn)("min-h-[60vh] flex items-center",h),children:(0,r.jsxs)("div",{className:(0,s.cn)("w-full","center"===x&&"text-center","right"===x&&"text-right"),children:[t&&(0,r.jsx)("p",{className:"text-secondary-amber font-medium text-lg mb-4 tracking-wide uppercase",children:t}),(0,r.jsx)("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-white mb-6 font-accent leading-tight",children:e}),a&&(0,r.jsx)("p",{className:"text-xl md:text-2xl text-neutral-white/90 mb-8 max-w-3xl mx-auto leading-relaxed",children:a}),(n||l)&&(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[n&&(0,r.jsx)(d.$,{variant:"accent",size:"lg",onClick:()=>u(n),leftIcon:n.icon,className:"min-w-[200px]",children:n.text}),l&&(0,r.jsx)(d.$,{variant:"ghost",size:"lg",onClick:()=>u(l),leftIcon:l.icon,className:"min-w-[200px] text-neutral-white border-neutral-white hover:bg-neutral-white hover:text-primary-sage",children:l.text})]})]})})},x=m},79250:(e,t,a)=>{Promise.resolve().then(a.bind(a,74290))},79551:e=>{"use strict";e.exports=require("url")},79713:(e,t,a)=>{"use strict";a.r(t),a.d(t,{Hero:()=>s,default:()=>i});var r=a(12907);let s=(0,r.registerClientReference)(function(){throw Error("Attempted to call Hero() from the server but Hero is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\Hero.tsx","Hero"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call the default export of \"O:\\\\VSCODE PROJECTS\\\\2025 NEW\\\\HeirloomCollective\\\\apothecary-extracts\\\\src\\\\components\\\\content\\\\Hero\\\\Hero.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\Hero.tsx","default")},81574:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>o});var r=a(65239),s=a(48088),i=a(88170),n=a.n(i),l=a(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);a.d(t,c);let o={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,28770)),"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,30173)),"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\about\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[447,46,658,767],()=>a(81574));module.exports=r})();