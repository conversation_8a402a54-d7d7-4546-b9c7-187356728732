"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[995],{215:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("pill",[["path",{d:"m10.5 20.5 10-10a4.95 4.95 0 1 0-7-7l-10 10a4.95 4.95 0 1 0 7 7Z",key:"wa1lgi"}],["path",{d:"m8.5 8.5 7 7",key:"rvfmvr"}]])},381:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},488:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},1007:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1264:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},1284:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1366:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("message-circle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},1539:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1788:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},1976:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},2108:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]])},2138:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},2355:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},2525:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2596:(e,r,t)=>{t.d(r,{$:()=>o});function o(){for(var e,r,t=0,o="",a=arguments.length;t<a;t++)(e=arguments[t])&&(r=function e(r){var t,o,a="";if("string"==typeof r||"number"==typeof r)a+=r;else if("object"==typeof r)if(Array.isArray(r)){var l=r.length;for(t=0;t<l;t++)r[t]&&(o=e(r[t]))&&(a&&(a+=" "),a+=o)}else for(o in r)r[o]&&(a&&(a+=" "),a+=o);return a}(e))&&(o&&(o+=" "),o+=r);return o}},2657:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2894:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])},2925:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("youtube",[["path",{d:"M2.5 17a24.12 24.12 0 0 1 0-10 2 2 0 0 1 1.4-1.4 49.56 49.56 0 0 1 16.2 0A2 2 0 0 1 21.5 7a24.12 24.12 0 0 1 0 10 2 2 0 0 1-1.4 1.4 49.55 49.55 0 0 1-16.2 0A2 2 0 0 1 2.5 17",key:"1q2vi4"}],["path",{d:"m10 15 5-3-5-3z",key:"1jp15x"}]])},3052:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3384:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]])},3717:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},3786:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4020:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("ellipsis-vertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},4186:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4357:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4416:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4783:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},4861:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5196:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5213:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("arrow-up-narrow-wide",[["path",{d:"m3 8 4-4 4 4",key:"11wl7u"}],["path",{d:"M7 4v16",key:"1glfcx"}],["path",{d:"M11 12h4",key:"q8tih4"}],["path",{d:"M11 16h7",key:"uosisv"}],["path",{d:"M11 20h10",key:"jvxblo"}]])},5339:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},5478:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("beaker",[["path",{d:"M4.5 3h15",key:"c7n0jr"}],["path",{d:"M6 3v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V3",key:"m1uhx7"}],["path",{d:"M6 14h12",key:"4cwo0f"}]])},5488:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("arrow-down-wide-narrow",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"M11 4h10",key:"1w87gc"}],["path",{d:"M11 8h7",key:"djye34"}],["path",{d:"M11 12h4",key:"q8tih4"}]])},5525:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},5623:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},5684:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},6474:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6516:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("share-2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},6932:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7340:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7550:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7712:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},7809:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},7863:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},7924:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8175:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},8564:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},8691:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("droplets",[["path",{d:"M7 16.3c2.2 0 4-1.83 4-4.05 0-1.16-.57-2.26-1.71-3.19S7.29 6.75 7 5.3c-.29 1.45-1.14 2.84-2.29 3.76S3 11.1 3 12.25c0 2.22 1.8 4.05 4 4.05z",key:"1ptgy4"}],["path",{d:"M12.56 6.6A10.97 10.97 0 0 0 14 3.02c.5 2.5 2 4.9 4 6.5s3 3.5 3 5.5a6.98 6.98 0 0 1-11.91 4.97",key:"1sl1rz"}]])},8749:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},8832:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("arrow-down",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},9037:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9074:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9420:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9688:(e,r,t)=>{t.d(r,{QP:()=>ed});let o=e=>{let r=s(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),a(t,r)||n(e)},getConflictingClassGroupIds:(e,r)=>{let a=t[e]||[];return r&&o[e]?[...a,...o[e]]:a}}},a=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),l=o?a(e.slice(1),o):void 0;if(l)return l;if(0===r.validators.length)return;let n=e.join("-");return r.validators.find(({validator:e})=>e(n))?.classGroupId},l=/^\[(.+)\]$/,n=e=>{if(l.test(e)){let r=l.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},s=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)i(t[e],o,e,r);return o},i=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:d(r,e)).classGroupId=t;return}if("function"==typeof e)return c(e)?void i(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,a])=>{i(a,d(r,e),t,o)})})},d=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},c=e=>e.isThemeGetter,p=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,a=(a,l)=>{t.set(a,l),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(a(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):a(e,r)}}},m=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,a=0,l=0;for(let n=0;n<e.length;n++){let s=e[n];if(0===o&&0===a){if(":"===s){t.push(e.slice(l,n)),l=n+1;continue}if("/"===s){r=n;continue}}"["===s?o++:"]"===s?o--:"("===s?a++:")"===s&&a--}let n=0===t.length?e:e.substring(l),s=h(n);return{modifiers:t,hasImportantModifier:s!==n,baseClassName:s,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},h=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,u=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},y=e=>({cache:p(e.cacheSize),parseClassName:m(e),sortModifiers:u(e),...o(e)}),k=/\s+/,f=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:a,sortModifiers:l}=r,n=[],s=e.trim().split(k),i="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:d,modifiers:c,hasImportantModifier:p,baseClassName:m,maybePostfixModifierPosition:h}=t(r);if(d){i=r+(i.length>0?" "+i:i);continue}let u=!!h,y=o(u?m.substring(0,h):m);if(!y){if(!u||!(y=o(m))){i=r+(i.length>0?" "+i:i);continue}u=!1}let k=l(c).join(":"),f=p?k+"!":k,b=f+y;if(n.includes(b))continue;n.push(b);let g=a(y,u);for(let e=0;e<g.length;++e){let r=g[e];n.push(f+r)}i=r+(i.length>0?" "+i:i)}return i};function b(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=g(e))&&(o&&(o+=" "),o+=r);return o}let g=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=g(e[o]))&&(t&&(t+=" "),t+=r);return t},x=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,v=/^\((?:(\w[\w-]*):)?(.+)\)$/i,A=/^\d+\/\d+$/,z=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,M=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,j=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,q=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,C=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,N=e=>A.test(e),V=e=>!!e&&!Number.isNaN(Number(e)),E=e=>!!e&&Number.isInteger(Number(e)),H=e=>e.endsWith("%")&&V(e.slice(0,-1)),P=e=>z.test(e),$=()=>!0,G=e=>M.test(e)&&!j.test(e),L=()=>!1,I=e=>q.test(e),S=e=>C.test(e),W=e=>!Z(e)&&!Q(e),_=e=>ee(e,ea,L),Z=e=>w.test(e),O=e=>ee(e,el,G),T=e=>ee(e,en,V),R=e=>ee(e,et,L),U=e=>ee(e,eo,S),B=e=>ee(e,ei,I),Q=e=>v.test(e),D=e=>er(e,el),F=e=>er(e,es),J=e=>er(e,et),K=e=>er(e,ea),X=e=>er(e,eo),Y=e=>er(e,ei,!0),ee=(e,r,t)=>{let o=w.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},er=(e,r,t=!1)=>{let o=v.exec(e);return!!o&&(o[1]?r(o[1]):t)},et=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,ea=e=>"length"===e||"size"===e||"bg-size"===e,el=e=>"length"===e,en=e=>"number"===e,es=e=>"family-name"===e,ei=e=>"shadow"===e;Symbol.toStringTag;let ed=function(e,...r){let t,o,a,l=function(s){return o=(t=y(r.reduce((e,r)=>r(e),e()))).cache.get,a=t.cache.set,l=n,n(s)};function n(e){let r=o(e);if(r)return r;let l=f(e,t);return a(e,l),l}return function(){return l(b.apply(null,arguments))}}(()=>{let e=x("color"),r=x("font"),t=x("text"),o=x("font-weight"),a=x("tracking"),l=x("leading"),n=x("breakpoint"),s=x("container"),i=x("spacing"),d=x("radius"),c=x("shadow"),p=x("inset-shadow"),m=x("text-shadow"),h=x("drop-shadow"),u=x("blur"),y=x("perspective"),k=x("aspect"),f=x("ease"),b=x("animate"),g=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...w(),Q,Z],A=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto","contain","none"],M=()=>[Q,Z,i],j=()=>[N,"full","auto",...M()],q=()=>[E,"none","subgrid",Q,Z],C=()=>["auto",{span:["full",E,Q,Z]},E,Q,Z],G=()=>[E,"auto",Q,Z],L=()=>["auto","min","max","fr",Q,Z],I=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],S=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...M()],er=()=>[N,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...M()],et=()=>[e,Q,Z],eo=()=>[...w(),J,R,{position:[Q,Z]}],ea=()=>["no-repeat",{repeat:["","x","y","space","round"]}],el=()=>["auto","cover","contain",K,_,{size:[Q,Z]}],en=()=>[H,D,O],es=()=>["","none","full",d,Q,Z],ei=()=>["",V,D,O],ed=()=>["solid","dashed","dotted","double"],ec=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ep=()=>[V,H,J,R],em=()=>["","none",u,Q,Z],eh=()=>["none",V,Q,Z],eu=()=>["none",V,Q,Z],ey=()=>[V,Q,Z],ek=()=>[N,"full",...M()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[P],breakpoint:[P],color:[$],container:[P],"drop-shadow":[P],ease:["in","out","in-out"],font:[W],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[P],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[P],shadow:[P],spacing:["px",V],text:[P],"text-shadow":[P],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",N,Z,Q,k]}],container:["container"],columns:[{columns:[V,Z,Q,s]}],"break-after":[{"break-after":g()}],"break-before":[{"break-before":g()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:A()}],"overflow-x":[{"overflow-x":A()}],"overflow-y":[{"overflow-y":A()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:j()}],"inset-x":[{"inset-x":j()}],"inset-y":[{"inset-y":j()}],start:[{start:j()}],end:[{end:j()}],top:[{top:j()}],right:[{right:j()}],bottom:[{bottom:j()}],left:[{left:j()}],visibility:["visible","invisible","collapse"],z:[{z:[E,"auto",Q,Z]}],basis:[{basis:[N,"full","auto",s,...M()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[V,N,"auto","initial","none",Z]}],grow:[{grow:["",V,Q,Z]}],shrink:[{shrink:["",V,Q,Z]}],order:[{order:[E,"first","last","none",Q,Z]}],"grid-cols":[{"grid-cols":q()}],"col-start-end":[{col:C()}],"col-start":[{"col-start":G()}],"col-end":[{"col-end":G()}],"grid-rows":[{"grid-rows":q()}],"row-start-end":[{row:C()}],"row-start":[{"row-start":G()}],"row-end":[{"row-end":G()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":L()}],"auto-rows":[{"auto-rows":L()}],gap:[{gap:M()}],"gap-x":[{"gap-x":M()}],"gap-y":[{"gap-y":M()}],"justify-content":[{justify:[...I(),"normal"]}],"justify-items":[{"justify-items":[...S(),"normal"]}],"justify-self":[{"justify-self":["auto",...S()]}],"align-content":[{content:["normal",...I()]}],"align-items":[{items:[...S(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...S(),{baseline:["","last"]}]}],"place-content":[{"place-content":I()}],"place-items":[{"place-items":[...S(),"baseline"]}],"place-self":[{"place-self":["auto",...S()]}],p:[{p:M()}],px:[{px:M()}],py:[{py:M()}],ps:[{ps:M()}],pe:[{pe:M()}],pt:[{pt:M()}],pr:[{pr:M()}],pb:[{pb:M()}],pl:[{pl:M()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":M()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":M()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[n]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,D,O]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,Q,T]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",H,Z]}],"font-family":[{font:[F,Z,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[a,Q,Z]}],"line-clamp":[{"line-clamp":[V,"none",Q,T]}],leading:[{leading:[l,...M()]}],"list-image":[{"list-image":["none",Q,Z]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Q,Z]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ed(),"wavy"]}],"text-decoration-thickness":[{decoration:[V,"from-font","auto",Q,O]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[V,"auto",Q,Z]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q,Z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q,Z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:ea()}],"bg-size":[{bg:el()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},E,Q,Z],radial:["",Q,Z],conic:[E,Q,Z]},X,U]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:en()}],"gradient-via-pos":[{via:en()}],"gradient-to-pos":[{to:en()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ed(),"hidden","none"]}],"divide-style":[{divide:[...ed(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ed(),"none","hidden"]}],"outline-offset":[{"outline-offset":[V,Q,Z]}],"outline-w":[{outline:["",V,D,O]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",c,Y,B]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",p,Y,B]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[V,O]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",m,Y,B]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[V,Q,Z]}],"mix-blend":[{"mix-blend":[...ec(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ec()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[V]}],"mask-image-linear-from-pos":[{"mask-linear-from":ep()}],"mask-image-linear-to-pos":[{"mask-linear-to":ep()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":ep()}],"mask-image-t-to-pos":[{"mask-t-to":ep()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":ep()}],"mask-image-r-to-pos":[{"mask-r-to":ep()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":ep()}],"mask-image-b-to-pos":[{"mask-b-to":ep()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":ep()}],"mask-image-l-to-pos":[{"mask-l-to":ep()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":ep()}],"mask-image-x-to-pos":[{"mask-x-to":ep()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":ep()}],"mask-image-y-to-pos":[{"mask-y-to":ep()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[Q,Z]}],"mask-image-radial-from-pos":[{"mask-radial-from":ep()}],"mask-image-radial-to-pos":[{"mask-radial-to":ep()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[V]}],"mask-image-conic-from-pos":[{"mask-conic-from":ep()}],"mask-image-conic-to-pos":[{"mask-conic-to":ep()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:ea()}],"mask-size":[{mask:el()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Q,Z]}],filter:[{filter:["","none",Q,Z]}],blur:[{blur:em()}],brightness:[{brightness:[V,Q,Z]}],contrast:[{contrast:[V,Q,Z]}],"drop-shadow":[{"drop-shadow":["","none",h,Y,B]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",V,Q,Z]}],"hue-rotate":[{"hue-rotate":[V,Q,Z]}],invert:[{invert:["",V,Q,Z]}],saturate:[{saturate:[V,Q,Z]}],sepia:[{sepia:["",V,Q,Z]}],"backdrop-filter":[{"backdrop-filter":["","none",Q,Z]}],"backdrop-blur":[{"backdrop-blur":em()}],"backdrop-brightness":[{"backdrop-brightness":[V,Q,Z]}],"backdrop-contrast":[{"backdrop-contrast":[V,Q,Z]}],"backdrop-grayscale":[{"backdrop-grayscale":["",V,Q,Z]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[V,Q,Z]}],"backdrop-invert":[{"backdrop-invert":["",V,Q,Z]}],"backdrop-opacity":[{"backdrop-opacity":[V,Q,Z]}],"backdrop-saturate":[{"backdrop-saturate":[V,Q,Z]}],"backdrop-sepia":[{"backdrop-sepia":["",V,Q,Z]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":M()}],"border-spacing-x":[{"border-spacing-x":M()}],"border-spacing-y":[{"border-spacing-y":M()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Q,Z]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[V,"initial",Q,Z]}],ease:[{ease:["linear","initial",f,Q,Z]}],delay:[{delay:[V,Q,Z]}],animate:[{animate:["none",b,Q,Z]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[y,Q,Z]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:eh()}],"rotate-x":[{"rotate-x":eh()}],"rotate-y":[{"rotate-y":eh()}],"rotate-z":[{"rotate-z":eh()}],scale:[{scale:eu()}],"scale-x":[{"scale-x":eu()}],"scale-y":[{"scale-y":eu()}],"scale-z":[{"scale-z":eu()}],"scale-3d":["scale-3d"],skew:[{skew:ey()}],"skew-x":[{"skew-x":ey()}],"skew-y":[{"skew-y":ey()}],transform:[{transform:[Q,Z,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ek()}],"translate-x":[{"translate-x":ek()}],"translate-y":[{"translate-y":ek()}],"translate-z":[{"translate-z":ek()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q,Z]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q,Z]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[V,D,O,T]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9869:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},9881:(e,r,t)=>{t.d(r,{A:()=>o});let o=(0,t(9946).A)("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]])},9946:(e,r,t)=>{t.d(r,{A:()=>p});var o=t(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},s=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},i=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,o.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:c="",children:p,iconNode:m,...h}=e;return(0,o.createElement)("svg",{ref:r,...d,width:a,height:a,stroke:t,strokeWidth:n?24*Number(l)/Number(a):l,className:s("lucide",c),...!p&&!i(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[r,t]=e;return(0,o.createElement)(r,t)}),...Array.isArray(p)?p:[p]])}),p=(e,r)=>{let t=(0,o.forwardRef)((t,l)=>{let{className:i,...d}=t;return(0,o.createElement)(c,{ref:l,iconNode:r,className:s("lucide-".concat(a(n(e))),"lucide-".concat(e),i),...d})});return t.displayName=n(e),t}}}]);