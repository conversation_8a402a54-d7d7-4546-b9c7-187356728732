[{"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\about\\page.tsx": "1", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\layout.tsx": "2", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\locations\\page.tsx": "3", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\page.tsx": "4", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\shop\\page.tsx": "5", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\Hero.tsx": "6", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\index.ts": "7", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\ProductGrid\\index.ts": "8", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\ProductGrid\\ProductCard.tsx": "9", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\ProductGrid\\ProductGrid.tsx": "10", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\forms\\NewsletterForm\\index.ts": "11", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\forms\\NewsletterForm\\NewsletterForm.tsx": "12", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Container\\Container.tsx": "13", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Container\\index.ts": "14", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Footer\\Footer.tsx": "15", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Footer\\index.ts": "16", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\Header.tsx": "17", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\index.ts": "18", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\LocationSelector.tsx": "19", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\MobileMenu.tsx": "20", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\NavigationMenu.tsx": "21", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Section\\index.ts": "22", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Section\\Section.tsx": "23", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AgeVerification\\AgeGate.tsx": "24", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AgeVerification\\index.ts": "25", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AppDownload\\AppDownload.tsx": "26", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AppDownload\\index.ts": "27", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\SocialLinks\\index.ts": "28", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\SocialLinks\\SocialLinks.tsx": "29", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Button\\Button.tsx": "30", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Button\\index.ts": "31", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Card\\Card.tsx": "32", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Card\\index.ts": "33", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Icon\\Icon.tsx": "34", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Icon\\index.ts": "35", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Modal\\index.ts": "36", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Modal\\Modal.tsx": "37", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\faq.ts": "38", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\navigation.ts": "39", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\products.ts": "40", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\testimonials.ts": "41", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useAgeVerification.ts": "42", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useCart.ts": "43", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useLocalStorage.ts": "44", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useLocation.ts": "45", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\lib\\constants.ts": "46", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\lib\\types.ts": "47", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\lib\\utils.ts": "48", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\ageVerificationStore.ts": "49", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\cartStore.ts": "50", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\locationStore.ts": "51", "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\uiStore.ts": "52"}, {"size": 11552, "mtime": 1752238989167, "results": "53", "hashOfConfig": "54"}, {"size": 2129, "mtime": 1752237994196, "results": "55", "hashOfConfig": "54"}, {"size": 12047, "mtime": 1752238160507, "results": "56", "hashOfConfig": "54"}, {"size": 5629, "mtime": 1752238977317, "results": "57", "hashOfConfig": "54"}, {"size": 11358, "mtime": 1752238113609, "results": "58", "hashOfConfig": "54"}, {"size": 3116, "mtime": 1752239000804, "results": "59", "hashOfConfig": "54"}, {"size": 81, "mtime": 1752236816832, "results": "60", "hashOfConfig": "54"}, {"size": 177, "mtime": 1752236971747, "results": "61", "hashOfConfig": "54"}, {"size": 2779, "mtime": 1752236965901, "results": "62", "hashOfConfig": "54"}, {"size": 1687, "mtime": 1752236951484, "results": "63", "hashOfConfig": "54"}, {"size": 146, "mtime": 1752237921115, "results": "64", "hashOfConfig": "54"}, {"size": 6935, "mtime": 1752238568128, "results": "65", "hashOfConfig": "54"}, {"size": 832, "mtime": 1752236751141, "results": "66", "hashOfConfig": "54"}, {"size": 101, "mtime": 1752236761302, "results": "67", "hashOfConfig": "54"}, {"size": 7466, "mtime": 1752237951488, "results": "68", "hashOfConfig": "54"}, {"size": 89, "mtime": 1752237968167, "results": "69", "hashOfConfig": "54"}, {"size": 4776, "mtime": 1752238611508, "results": "70", "hashOfConfig": "54"}, {"size": 260, "mtime": 1752237059986, "results": "71", "hashOfConfig": "54"}, {"size": 5888, "mtime": 1752238657969, "results": "72", "hashOfConfig": "54"}, {"size": 4755, "mtime": 1752238647138, "results": "73", "hashOfConfig": "54"}, {"size": 2823, "mtime": 1752238635087, "results": "74", "hashOfConfig": "54"}, {"size": 93, "mtime": 1752236783424, "results": "75", "hashOfConfig": "54"}, {"size": 1971, "mtime": 1752236776144, "results": "76", "hashOfConfig": "54"}, {"size": 5614, "mtime": 1752238597942, "results": "77", "hashOfConfig": "54"}, {"size": 74, "mtime": 1752237091496, "results": "78", "hashOfConfig": "54"}, {"size": 5165, "mtime": 1752237833257, "results": "79", "hashOfConfig": "54"}, {"size": 109, "mtime": 1752237843075, "results": "80", "hashOfConfig": "54"}, {"size": 201, "mtime": 1752237880431, "results": "81", "hashOfConfig": "54"}, {"size": 3189, "mtime": 1752237867743, "results": "82", "hashOfConfig": "54"}, {"size": 2527, "mtime": 1752239131579, "results": "83", "hashOfConfig": "54"}, {"size": 89, "mtime": 1752236690391, "results": "84", "hashOfConfig": "54"}, {"size": 2683, "mtime": 1752236706649, "results": "85", "hashOfConfig": "54"}, {"size": 167, "mtime": 1752236713424, "results": "86", "hashOfConfig": "54"}, {"size": 2895, "mtime": 1752236661137, "results": "87", "hashOfConfig": "54"}, {"size": 96, "mtime": 1752236667611, "results": "88", "hashOfConfig": "54"}, {"size": 137, "mtime": 1752236741028, "results": "89", "hashOfConfig": "54"}, {"size": 4369, "mtime": 1752238623524, "results": "90", "hashOfConfig": "54"}, {"size": 6761, "mtime": 1752236937188, "results": "91", "hashOfConfig": "54"}, {"size": 3779, "mtime": 1752236836247, "results": "92", "hashOfConfig": "54"}, {"size": 2880, "mtime": 1752236855332, "results": "93", "hashOfConfig": "54"}, {"size": 4476, "mtime": 1752236903768, "results": "94", "hashOfConfig": "54"}, {"size": 2304, "mtime": 1752238681366, "results": "95", "hashOfConfig": "54"}, {"size": 5653, "mtime": 1752238710599, "results": "96", "hashOfConfig": "54"}, {"size": 3722, "mtime": 1752238669684, "results": "97", "hashOfConfig": "54"}, {"size": 7874, "mtime": 1752238696067, "results": "98", "hashOfConfig": "54"}, {"size": 7294, "mtime": 1752236419425, "results": "99", "hashOfConfig": "54"}, {"size": 9943, "mtime": 1752236468148, "results": "100", "hashOfConfig": "54"}, {"size": 6212, "mtime": 1752236382918, "results": "101", "hashOfConfig": "54"}, {"size": 1714, "mtime": 1752236481611, "results": "102", "hashOfConfig": "54"}, {"size": 6071, "mtime": 1752236515746, "results": "103", "hashOfConfig": "54"}, {"size": 1642, "mtime": 1752236491888, "results": "104", "hashOfConfig": "54"}, {"size": 4032, "mtime": 1752236537159, "results": "105", "hashOfConfig": "54"}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hh1w4i", {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\about\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\layout.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\locations\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\page.tsx", ["262"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\shop\\page.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\Hero.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\ProductGrid\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\ProductGrid\\ProductCard.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\ProductGrid\\ProductGrid.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\forms\\NewsletterForm\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\forms\\NewsletterForm\\NewsletterForm.tsx", ["263", "264", "265", "266"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Container\\Container.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Container\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Footer\\Footer.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Footer\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\Header.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\LocationSelector.tsx", ["267", "268", "269"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\MobileMenu.tsx", ["270"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\NavigationMenu.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Section\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Section\\Section.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AgeVerification\\AgeGate.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AgeVerification\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AppDownload\\AppDownload.tsx", ["271"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AppDownload\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\SocialLinks\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\SocialLinks\\SocialLinks.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Button\\Button.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Button\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Card\\Card.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Card\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Icon\\Icon.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Icon\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Modal\\index.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\ui\\Modal\\Modal.tsx", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\faq.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\navigation.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\products.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\data\\testimonials.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useAgeVerification.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useCart.ts", ["272", "273", "274", "275", "276", "277"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useLocalStorage.ts", ["278", "279", "280", "281", "282", "283", "284"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\hooks\\useLocation.ts", ["285", "286", "287"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\lib\\constants.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\lib\\types.ts", ["288", "289", "290"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\lib\\utils.ts", ["291", "292", "293", "294", "295", "296"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\ageVerificationStore.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\cartStore.ts", ["297", "298"], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\locationStore.ts", [], [], "O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\store\\uiStore.ts", [], [], {"ruleId": "299", "severity": 1, "message": "300", "line": 55, "column": 35, "nodeType": "301", "messageId": "302", "suggestions": "303"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 4, "column": 10, "nodeType": null, "messageId": "306", "endLine": 4, "endColumn": 12}, {"ruleId": "304", "severity": 1, "message": "307", "line": 79, "column": 14, "nodeType": null, "messageId": "306", "endLine": 79, "endColumn": 19}, {"ruleId": "308", "severity": 1, "message": "309", "line": 86, "column": 70, "nodeType": "310", "messageId": "311", "endLine": 86, "endColumn": 73, "suggestions": "312"}, {"ruleId": "299", "severity": 1, "message": "300", "line": 113, "column": 14, "nodeType": "301", "messageId": "302", "suggestions": "313"}, {"ruleId": "304", "severity": 1, "message": "314", "line": 4, "column": 17, "nodeType": null, "messageId": "306", "endLine": 4, "endColumn": 28}, {"ruleId": "304", "severity": 1, "message": "315", "line": 18, "column": 5, "nodeType": null, "messageId": "306", "endLine": 18, "endColumn": 15}, {"ruleId": "308", "severity": 1, "message": "309", "line": 21, "column": 43, "nodeType": "310", "messageId": "311", "endLine": 21, "endColumn": 46, "suggestions": "316"}, {"ruleId": "304", "severity": 1, "message": "305", "line": 5, "column": 10, "nodeType": null, "messageId": "306", "endLine": 5, "endColumn": 12}, {"ruleId": "304", "severity": 1, "message": "317", "line": 2, "column": 8, "nodeType": null, "messageId": "306", "endLine": 2, "endColumn": 13}, {"ruleId": "304", "severity": 1, "message": "318", "line": 23, "column": 5, "nodeType": null, "messageId": "306", "endLine": 23, "endColumn": 19}, {"ruleId": "304", "severity": 1, "message": "319", "line": 24, "column": 5, "nodeType": null, "messageId": "306", "endLine": 24, "endColumn": 20}, {"ruleId": "304", "severity": 1, "message": "320", "line": 77, "column": 14, "nodeType": null, "messageId": "306", "endLine": 77, "endColumn": 17}, {"ruleId": "304", "severity": 1, "message": "320", "line": 88, "column": 14, "nodeType": null, "messageId": "306", "endLine": 88, "endColumn": 17}, {"ruleId": "304", "severity": 1, "message": "320", "line": 103, "column": 14, "nodeType": null, "messageId": "306", "endLine": 103, "endColumn": 17}, {"ruleId": "304", "severity": 1, "message": "321", "line": 179, "column": 16, "nodeType": null, "messageId": "306", "endLine": 179, "endColumn": 20}, {"ruleId": "322", "severity": 1, "message": "323", "line": 95, "column": 16, "nodeType": "324", "messageId": "325", "endLine": 95, "endColumn": 19, "fix": "326"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 122, "column": 24, "nodeType": "310", "messageId": "311", "endLine": 122, "endColumn": 27, "suggestions": "327"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 122, "column": 39, "nodeType": "310", "messageId": "311", "endLine": 122, "endColumn": 42, "suggestions": "328"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 127, "column": 19, "nodeType": "310", "messageId": "311", "endLine": 127, "endColumn": 22, "suggestions": "329"}, {"ruleId": "304", "severity": 1, "message": "307", "line": 131, "column": 14, "nodeType": null, "messageId": "306", "endLine": 131, "endColumn": 19}, {"ruleId": "308", "severity": 1, "message": "309", "line": 137, "column": 23, "nodeType": "310", "messageId": "311", "endLine": 137, "endColumn": 26, "suggestions": "330"}, {"ruleId": "304", "severity": 1, "message": "307", "line": 140, "column": 18, "nodeType": null, "messageId": "306", "endLine": 140, "endColumn": 23}, {"ruleId": "304", "severity": 1, "message": "331", "line": 27, "column": 33, "nodeType": null, "messageId": "306", "endLine": 27, "endColumn": 42}, {"ruleId": "332", "severity": 1, "message": "333", "line": 33, "column": 6, "nodeType": "334", "endLine": 33, "endColumn": 8, "suggestions": "335"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 208, "column": 59, "nodeType": "310", "messageId": "311", "endLine": 208, "endColumn": 62, "suggestions": "336"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 158, "column": 27, "nodeType": "310", "messageId": "311", "endLine": 158, "endColumn": 30, "suggestions": "337"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 427, "column": 34, "nodeType": "310", "messageId": "311", "endLine": 427, "endColumn": 37, "suggestions": "338"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 434, "column": 40, "nodeType": "310", "messageId": "311", "endLine": 434, "endColumn": 43, "suggestions": "339"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 76, "column": 46, "nodeType": "310", "messageId": "311", "endLine": 76, "endColumn": 49, "suggestions": "340"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 76, "column": 56, "nodeType": "310", "messageId": "311", "endLine": 76, "endColumn": 59, "suggestions": "341"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 212, "column": 32, "nodeType": "310", "messageId": "311", "endLine": 212, "endColumn": 35, "suggestions": "342"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 225, "column": 62, "nodeType": "310", "messageId": "311", "endLine": 225, "endColumn": 65, "suggestions": "343"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 226, "column": 72, "nodeType": "310", "messageId": "311", "endLine": 226, "endColumn": 75, "suggestions": "344"}, {"ruleId": "308", "severity": 1, "message": "309", "line": 228, "column": 29, "nodeType": "310", "messageId": "311", "endLine": 228, "endColumn": 32, "suggestions": "345"}, {"ruleId": "304", "severity": 1, "message": "346", "line": 4, "column": 24, "nodeType": null, "messageId": "306", "endLine": 4, "endColumn": 38}, {"ruleId": "304", "severity": 1, "message": "347", "line": 5, "column": 31, "nodeType": null, "messageId": "306", "endLine": 5, "endColumn": 39}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["348", "349", "350", "351"], "@typescript-eslint/no-unused-vars", "'cn' is defined but never used.", "unusedVar", "'error' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["352", "353"], ["354", "355", "356", "357"], "'ModalHeader' is defined but never used.", "'hasService' is assigned a value but never used.", ["358", "359"], "'Image' is defined but never used.", "'switchLocation' is assigned a value but never used.", "'calculateTotals' is assigned a value but never used.", "'err' is defined but never used.", "'item' is assigned a value but never used.", "prefer-const", "'key' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "360", "text": "361"}, ["362", "363"], ["364", "365"], ["366", "367"], ["368", "369"], "'clearCart' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadLocations'. Either include it or remove the dependency array.", "ArrayExpression", ["370"], ["371", "372"], ["373", "374"], ["375", "376"], ["377", "378"], ["379", "380"], ["381", "382"], ["383", "384"], ["385", "386"], ["387", "388"], ["389", "390"], "'calculateTotal' is defined but never used.", "'Location' is defined but never used.", {"messageId": "391", "data": "392", "fix": "393", "desc": "394"}, {"messageId": "391", "data": "395", "fix": "396", "desc": "397"}, {"messageId": "391", "data": "398", "fix": "399", "desc": "400"}, {"messageId": "391", "data": "401", "fix": "402", "desc": "403"}, {"messageId": "404", "fix": "405", "desc": "406"}, {"messageId": "407", "fix": "408", "desc": "409"}, {"messageId": "391", "data": "410", "fix": "411", "desc": "394"}, {"messageId": "391", "data": "412", "fix": "413", "desc": "397"}, {"messageId": "391", "data": "414", "fix": "415", "desc": "400"}, {"messageId": "391", "data": "416", "fix": "417", "desc": "403"}, {"messageId": "404", "fix": "418", "desc": "406"}, {"messageId": "407", "fix": "419", "desc": "409"}, [2318, 2325], "const key", {"messageId": "404", "fix": "420", "desc": "406"}, {"messageId": "407", "fix": "421", "desc": "409"}, {"messageId": "404", "fix": "422", "desc": "406"}, {"messageId": "407", "fix": "423", "desc": "409"}, {"messageId": "404", "fix": "424", "desc": "406"}, {"messageId": "407", "fix": "425", "desc": "409"}, {"messageId": "404", "fix": "426", "desc": "406"}, {"messageId": "407", "fix": "427", "desc": "409"}, {"desc": "428", "fix": "429"}, {"messageId": "404", "fix": "430", "desc": "406"}, {"messageId": "407", "fix": "431", "desc": "409"}, {"messageId": "404", "fix": "432", "desc": "406"}, {"messageId": "407", "fix": "433", "desc": "409"}, {"messageId": "404", "fix": "434", "desc": "406"}, {"messageId": "407", "fix": "435", "desc": "409"}, {"messageId": "404", "fix": "436", "desc": "406"}, {"messageId": "407", "fix": "437", "desc": "409"}, {"messageId": "404", "fix": "438", "desc": "406"}, {"messageId": "407", "fix": "439", "desc": "409"}, {"messageId": "404", "fix": "440", "desc": "406"}, {"messageId": "407", "fix": "441", "desc": "409"}, {"messageId": "404", "fix": "442", "desc": "406"}, {"messageId": "407", "fix": "443", "desc": "409"}, {"messageId": "404", "fix": "444", "desc": "406"}, {"messageId": "407", "fix": "445", "desc": "409"}, {"messageId": "404", "fix": "446", "desc": "406"}, {"messageId": "407", "fix": "447", "desc": "409"}, {"messageId": "404", "fix": "448", "desc": "406"}, {"messageId": "407", "fix": "449", "desc": "409"}, "replaceWithAlt", {"alt": "450"}, {"range": "451", "text": "452"}, "Replace with `&apos;`.", {"alt": "453"}, {"range": "454", "text": "455"}, "Replace with `&lsquo;`.", {"alt": "456"}, {"range": "457", "text": "458"}, "Replace with `&#39;`.", {"alt": "459"}, {"range": "460", "text": "461"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "462", "text": "463"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "464", "text": "465"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"alt": "450"}, {"range": "466", "text": "467"}, {"alt": "453"}, {"range": "468", "text": "469"}, {"alt": "456"}, {"range": "470", "text": "471"}, {"alt": "459"}, {"range": "472", "text": "473"}, {"range": "474", "text": "463"}, {"range": "475", "text": "465"}, {"range": "476", "text": "463"}, {"range": "477", "text": "465"}, {"range": "478", "text": "463"}, {"range": "479", "text": "465"}, {"range": "480", "text": "463"}, {"range": "481", "text": "465"}, {"range": "482", "text": "463"}, {"range": "483", "text": "465"}, "Update the dependencies array to be: [loadLocations]", {"range": "484", "text": "485"}, {"range": "486", "text": "463"}, {"range": "487", "text": "465"}, {"range": "488", "text": "463"}, {"range": "489", "text": "465"}, {"range": "490", "text": "463"}, {"range": "491", "text": "465"}, {"range": "492", "text": "463"}, {"range": "493", "text": "465"}, {"range": "494", "text": "463"}, {"range": "495", "text": "465"}, {"range": "496", "text": "463"}, {"range": "497", "text": "465"}, {"range": "498", "text": "463"}, {"range": "499", "text": "465"}, {"range": "500", "text": "463"}, {"range": "501", "text": "465"}, {"range": "502", "text": "463"}, {"range": "503", "text": "465"}, {"range": "504", "text": "463"}, {"range": "505", "text": "465"}, "&apos;", [1862, 1917], "\n            Healing Through Nature&apos;s Wisdom\n          ", "&lsquo;", [1862, 1917], "\n            Healing Through Nature&lsquo;s <PERSON>\n          ", "&#39;", [1862, 1917], "\n            Healing Through Nature&#39;s <PERSON>\n          ", "&rsquo;", [1862, 1917], "\n            Healing Through Nature&rsquo;s <PERSON>\n          ", [2336, 2339], "unknown", [2336, 2339], "never", [3137, 3246], "\n          You&apos;ve successfully subscribed to our newsletter. Check your email for a welcome message.\n        ", [3137, 3246], "\n          You&lsquo;ve successfully subscribed to our newsletter. Check your email for a welcome message.\n        ", [3137, 3246], "\n          You&#39;ve successfully subscribed to our newsletter. Check your email for a welcome message.\n        ", [3137, 3246], "\n          You&rsquo;ve successfully subscribed to our newsletter. Check your email for a welcome message.\n        ", [674, 677], [674, 677], [2923, 2926], [2923, 2926], [2938, 2941], [2938, 2941], [3041, 3044], [3041, 3044], [3314, 3317], [3314, 3317], [809, 811], "[loadLocations]", [7252, 7255], [7252, 7255], [3033, 3036], [3033, 3036], [8086, 8089], [8086, 8089], [8205, 8208], [8205, 8208], [1678, 1681], [1678, 1681], [1688, 1691], [1688, 1691], [4726, 4729], [4726, 4729], [5178, 5181], [5178, 5181], [5254, 5257], [5254, 5257], [5320, 5323], [5320, 5323]]