import type { ProductCategory } from '@/lib/types';

export interface ProductCategoryData {
  id: ProductCategory;
  name: string;
  description: string;
  image: string;
  href: string;
  featured: boolean;
  order: number;
}

export const productCategories: ProductCategoryData[] = [
  {
    id: 'flower',
    name: 'Premium Flower',
    description: 'Artisanal cannabis flower strains, carefully cultivated for optimal potency and flavor profiles.',
    image: '/images/products/flower.jpg',
    href: '/shop/flower',
    featured: true,
    order: 1,
  },
  {
    id: 'pre-rolls',
    name: 'Pre-rolls',
    description: 'Expertly rolled joints featuring our finest flower strains, ready for immediate enjoyment.',
    image: '/images/products/pre-rolls.jpg',
    href: '/shop/pre-rolls',
    featured: true,
    order: 2,
  },
  {
    id: 'concentrates',
    name: 'Concentrates',
    description: 'High-potency extracts including live resin, rosin, and distillate for experienced users.',
    image: '/images/products/concentrates.jpg',
    href: '/shop/concentrates',
    featured: true,
    order: 3,
  },
  {
    id: 'edibles',
    name: 'Edibles',
    description: 'Precisely dosed cannabis-infused foods and beverages for a controlled, long-lasting experience.',
    image: '/images/products/edibles.jpg',
    href: '/shop/edibles',
    featured: true,
    order: 4,
  },
  {
    id: 'tinctures',
    name: 'Tinctures',
    description: 'Fast-acting liquid extracts perfect for precise dosing and discreet consumption.',
    image: '/images/products/tinctures.jpg',
    href: '/shop/tinctures',
    featured: true,
    order: 5,
  },
  {
    id: 'topicals',
    name: 'Topicals',
    description: 'Cannabis-infused creams, balms, and lotions for localized relief without psychoactive effects.',
    image: '/images/products/topicals.jpg',
    href: '/shop/topicals',
    featured: true,
    order: 6,
  },
  {
    id: 'vaporizers',
    name: 'Vaporizers',
    description: 'Premium vape cartridges and disposable pens for clean, convenient consumption.',
    image: '/images/products/vaporizers.jpg',
    href: '/shop/vaporizers',
    featured: false,
    order: 7,
  },
  {
    id: 'accessories',
    name: 'Accessories',
    description: 'High-quality smoking accessories, storage solutions, and consumption tools.',
    image: '/images/products/accessories.jpg',
    href: '/shop/accessories',
    featured: false,
    order: 8,
  },
];

export const featuredProducts = productCategories.filter(category => category.featured);

export const getProductCategoryById = (id: ProductCategory): ProductCategoryData | undefined => {
  return productCategories.find(category => category.id === id);
};

export const getProductCategoriesByIds = (ids: ProductCategory[]): ProductCategoryData[] => {
  return productCategories.filter(category => ids.includes(category.id));
};
