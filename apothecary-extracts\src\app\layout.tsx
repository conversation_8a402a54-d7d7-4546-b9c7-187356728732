import type { Metadata } from "next";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { AgeGate } from "@/components/specialized/AgeVerification";

export const metadata: Metadata = {
  title: "Apothecary Extracts | Premium Medicinal Cannabis Dispensary",
  description: "Premium medicinal cannabis dispensary specializing in artisanal extracts and healing botanicals. Located in Boston and Cambridge, Massachusetts.",
  keywords: "cannabis, dispensary, medical marijuana, recreational cannabis, Boston, Cambridge, Massachusetts, CBD, THC, extracts, tinctures, edibles",
  authors: [{ name: "Apothecary Extracts" }],
  creator: "Apothecary Extracts",
  publisher: "Apothecary Extracts",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://apothecaryextracts.com",
    siteName: "Apothecary Extracts",
    title: "Apothecary Extracts | Premium Medicinal Cannabis Dispensary",
    description: "Premium medicinal cannabis dispensary specializing in artisanal extracts and healing botanicals.",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Apothecary Extracts - Premium Cannabis Dispensary",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@apothecaryextracts",
    creator: "@apothecaryextracts",
    title: "Apothecary Extracts | Premium Medicinal Cannabis Dispensary",
    description: "Premium medicinal cannabis dispensary specializing in artisanal extracts and healing botanicals.",
    images: ["/images/twitter-image.jpg"],
  },
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#87A96B",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="font-secondary antialiased">
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
        <AgeGate />
      </body>
    </html>
  );
}
