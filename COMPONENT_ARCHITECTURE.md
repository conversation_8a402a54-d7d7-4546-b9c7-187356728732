# Component Architecture Map

## Overview
This document maps all reusable UI components identified from The Heirloom Collective website analysis. Each component includes its purpose, props interface, variants, and usage guidelines.

## Component Hierarchy

### Layout Components

#### `Header`
**Purpose**: Main site navigation and branding
**Location**: Top of every page
**Props**:
```typescript
interface HeaderProps {
  isMenuOpen?: boolean;
  onMenuToggle?: () => void;
  currentLocation?: 'hadley' | 'bernardston';
  preOrderStatus?: string;
}
```
**Features**:
- Logo with link to home
- Primary navigation menu
- Mobile hamburger menu
- Location selector
- Pre-order status indicator
- Search functionality

#### `Footer`
**Purpose**: Site-wide footer with links and information
**Location**: Bottom of every page
**Props**:
```typescript
interface FooterProps {
  showNewsletter?: boolean;
  showAppDownload?: boolean;
}
```
**Features**:
- Logo and social links
- Product categories
- Quick links
- App download buttons
- Newsletter signup
- Legal links and copyright

#### `Container`
**Purpose**: Consistent content width and padding
**Props**:
```typescript
interface ContainerProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  padding?: boolean;
  children: React.ReactNode;
}
```

#### `Section`
**Purpose**: Consistent section spacing and layout
**Props**:
```typescript
interface SectionProps {
  background?: 'white' | 'light' | 'dark' | 'image';
  backgroundImage?: string;
  padding?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}
```

### Navigation Components

#### `NavigationMenu`
**Purpose**: Primary site navigation
**Props**:
```typescript
interface NavigationMenuProps {
  items: NavigationItem[];
  orientation?: 'horizontal' | 'vertical';
  variant?: 'desktop' | 'mobile';
}
```

#### `MobileMenu`
**Purpose**: Mobile hamburger menu overlay
**Props**:
```typescript
interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  navigationItems: NavigationItem[];
}
```

#### `Breadcrumbs`
**Purpose**: Page hierarchy navigation
**Props**:
```typescript
interface BreadcrumbsProps {
  items: BreadcrumbItem[];
  separator?: string;
}
```

### UI Components

#### `Button`
**Purpose**: Primary interactive element
**Props**:
```typescript
interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}
```
**Variants**:
- Primary: Green background, white text
- Secondary: White background, green border
- Ghost: Transparent background, green text
- Link: No background, underline on hover

#### `Card`
**Purpose**: Content container with consistent styling
**Props**:
```typescript
interface CardProps {
  variant?: 'default' | 'product' | 'location' | 'testimonial';
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}
```

#### `Modal`
**Purpose**: Overlay dialogs and popups
**Props**:
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  children: React.ReactNode;
}
```

#### `AgeGate`
**Purpose**: Age verification modal for compliance
**Props**:
```typescript
interface AgeGateProps {
  isOpen: boolean;
  onVerify: (verified: boolean) => void;
  logo?: string;
  socialLinks?: SocialLink[];
}
```

### Form Components

#### `Input`
**Purpose**: Text input field
**Props**:
```typescript
interface InputProps {
  type?: 'text' | 'email' | 'password' | 'tel' | 'url';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}
```

#### `Select`
**Purpose**: Dropdown selection
**Props**:
```typescript
interface SelectProps {
  options: SelectOption[];
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  error?: string;
  disabled?: boolean;
}
```

#### `SearchBar`
**Purpose**: Product search functionality
**Props**:
```typescript
interface SearchBarProps {
  placeholder?: string;
  onSearch?: (query: string) => void;
  suggestions?: string[];
  showSuggestions?: boolean;
}
```

### Content Components

#### `Hero`
**Purpose**: Large banner section with CTA
**Props**:
```typescript
interface HeroProps {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  primaryCTA?: ButtonProps;
  secondaryCTA?: ButtonProps;
  overlay?: boolean;
}
```

#### `ProductGrid`
**Purpose**: Grid layout for product categories
**Props**:
```typescript
interface ProductGridProps {
  products: ProductCategory[];
  columns?: 2 | 3 | 4 | 6;
  gap?: 'sm' | 'md' | 'lg';
}
```

#### `ProductCard`
**Purpose**: Individual product category display
**Props**:
```typescript
interface ProductCardProps {
  title: string;
  image: string;
  link: string;
  description?: string;
}
```

#### `LocationCard`
**Purpose**: Dispensary location information
**Props**:
```typescript
interface LocationCardProps {
  name: string;
  address: string;
  image: string;
  services: ('med' | 'rec')[];
  hours?: string;
  phone?: string;
}
```

#### `TestimonialCard`
**Purpose**: Customer review display
**Props**:
```typescript
interface TestimonialCardProps {
  content: string;
  author: string;
  source: string;
  rating?: number;
  location?: string;
}
```

#### `TestimonialCarousel`
**Purpose**: Rotating testimonials display
**Props**:
```typescript
interface TestimonialCarouselProps {
  testimonials: Testimonial[];
  autoPlay?: boolean;
  interval?: number;
  showDots?: boolean;
  showArrows?: boolean;
}
```

#### `FAQ`
**Purpose**: Frequently asked questions
**Props**:
```typescript
interface FAQProps {
  items: FAQItem[];
  allowMultiple?: boolean;
  defaultOpen?: number[];
}
```

#### `Accordion`
**Purpose**: Collapsible content sections
**Props**:
```typescript
interface AccordionProps {
  items: AccordionItem[];
  allowMultiple?: boolean;
  variant?: 'default' | 'bordered' | 'filled';
}
```

### Utility Components

#### `Icon`
**Purpose**: Consistent icon display
**Props**:
```typescript
interface IconProps {
  name: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  color?: string;
  className?: string;
}
```

#### `Badge`
**Purpose**: Status indicators and labels
**Props**:
```typescript
interface BadgeProps {
  variant?: 'default' | 'success' | 'warning' | 'error' | 'info';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}
```

#### `Spinner`
**Purpose**: Loading indicator
**Props**:
```typescript
interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}
```

#### `Image`
**Purpose**: Optimized image component
**Props**:
```typescript
interface ImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  lazy?: boolean;
  placeholder?: string;
  className?: string;
}
```

### Specialized Components

#### `NewsletterSignup`
**Purpose**: Email subscription form
**Props**:
```typescript
interface NewsletterSignupProps {
  title?: string;
  description?: string;
  placeholder?: string;
  onSubmit?: (email: string) => void;
  variant?: 'inline' | 'card' | 'modal';
}
```

#### `AppDownload`
**Purpose**: Mobile app promotion
**Props**:
```typescript
interface AppDownloadProps {
  title?: string;
  description?: string;
  appStoreUrl?: string;
  playStoreUrl?: string;
  showQR?: boolean;
}
```

#### `SocialLinks`
**Purpose**: Social media link collection
**Props**:
```typescript
interface SocialLinksProps {
  links: SocialLink[];
  variant?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
}
```

#### `LocationSelector`
**Purpose**: Store location switcher
**Props**:
```typescript
interface LocationSelectorProps {
  locations: Location[];
  currentLocation?: string;
  onLocationChange?: (locationId: string) => void;
  showCart?: boolean;
}
```

## Component Composition Patterns

### Page Templates
- **HomePage**: Hero + ProductGrid + Mission + Testimonials + Newsletter
- **AboutPage**: Hero + Content + FAQ + Mission
- **ShopPage**: Header + ProductGrid + Filters + Footer
- **LocationPage**: Hero + LocationInfo + Hours + Contact

### Layout Patterns
- **Full Width**: Hero sections, testimonials
- **Container**: Most content sections
- **Grid**: Product categories, testimonials
- **Flex**: Navigation, cards, buttons

### Responsive Behavior
- **Mobile First**: All components designed mobile-first
- **Breakpoint Specific**: Different layouts per breakpoint
- **Progressive Enhancement**: Enhanced features on larger screens

## Implementation Guidelines

### Component Structure
```
components/
├── layout/          # Layout components
├── ui/              # Basic UI components
├── forms/           # Form-related components
├── content/         # Content display components
├── specialized/     # Business-specific components
└── utils/           # Utility components
```

### Naming Conventions
- PascalCase for component names
- camelCase for props
- kebab-case for CSS classes
- SCREAMING_SNAKE_CASE for constants

### Props Guidelines
- Use TypeScript interfaces for all props
- Provide sensible defaults
- Make components as flexible as possible
- Follow composition over inheritance

### Testing Strategy
- Unit tests for all components
- Integration tests for complex interactions
- Accessibility testing for all interactive elements
- Visual regression testing for UI consistency
