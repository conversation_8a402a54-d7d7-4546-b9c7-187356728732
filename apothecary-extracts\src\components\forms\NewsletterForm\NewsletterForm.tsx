'use client';

import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { Card, CardContent } from '@/components/ui/Card';
import { isValidEmail } from '@/lib/utils';
import { toast } from '@/store/uiStore';

export interface NewsletterFormProps {
  title?: string;
  description?: string;
  placeholder?: string;
  variant?: 'inline' | 'card' | 'modal';
  showPreferences?: boolean;
  onSubmit?: (data: NewsletterFormData) => Promise<void>;
  className?: string;
}

export interface NewsletterFormData {
  email: string;
  firstName?: string;
  preferences: {
    deals: boolean;
    newProducts: boolean;
    events: boolean;
    education: boolean;
  };
}

export const NewsletterForm: React.FC<NewsletterFormProps> = ({
  title = "Stay Connected",
  description = "Get the latest updates on new products, exclusive deals, and cannabis education delivered to your inbox.",
  placeholder = "Enter your email address",
  variant = 'card',
  showPreferences = false,
  onSubmit,
  className,
}) => {
  const [formData, setFormData] = useState<NewsletterFormData>({
    email: '',
    firstName: '',
    preferences: {
      deals: true,
      newProducts: true,
      events: false,
      education: true,
    },
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email) {
      toast.error('Email Required', 'Please enter your email address');
      return;
    }

    if (!isValidEmail(formData.email)) {
      toast.error('Invalid Email', 'Please enter a valid email address');
      return;
    }

    setIsSubmitting(true);

    try {
      if (onSubmit) {
        await onSubmit(formData);
      } else {
        // Mock API call
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
      setIsSubmitted(true);
      toast.success('Success!', 'Thank you for subscribing to our newsletter');
    } catch (error) {
      toast.error('Error', 'Failed to subscribe. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof NewsletterFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handlePreferenceChange = (preference: keyof NewsletterFormData['preferences']) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [preference]: !prev.preferences[preference],
      },
    }));
  };

  if (isSubmitted) {
    const successContent = (
      <div className="text-center py-8">
        <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon name="check" size="xl" className="text-success" />
        </div>
        <h3 className="text-xl font-semibold text-neutral-charcoal mb-2">
          Welcome to the Family!
        </h3>
        <p className="text-neutral-charcoal/70">
          You've successfully subscribed to our newsletter. Check your email for a welcome message.
        </p>
      </div>
    );

    if (variant === 'card') {
      return (
        <Card className={className}>
          <CardContent>
            {successContent}
          </CardContent>
        </Card>
      );
    }

    return <div className={className}>{successContent}</div>;
  }

  const formContent = (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-xl md:text-2xl font-bold text-neutral-charcoal mb-2 font-accent">
          {title}
        </h3>
        <p className="text-neutral-charcoal/70">
          {description}
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Email Input */}
        <div className="flex flex-col sm:flex-row gap-3">
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder={placeholder}
            className="flex-1 px-4 py-3 border border-neutral-stone rounded-lg focus:ring-2 focus:ring-primary-sage focus:border-transparent transition-colors duration-200"
            required
          />
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isSubmitting}
            className="sm:px-8"
          >
            Subscribe
          </Button>
        </div>

        {/* First Name (Optional) */}
        {showPreferences && (
          <input
            type="text"
            value={formData.firstName}
            onChange={(e) => handleInputChange('firstName', e.target.value)}
            placeholder="First name (optional)"
            className="w-full px-4 py-3 border border-neutral-stone rounded-lg focus:ring-2 focus:ring-primary-sage focus:border-transparent transition-colors duration-200"
          />
        )}

        {/* Preferences */}
        {showPreferences && (
          <div className="space-y-3">
            <p className="text-sm font-medium text-neutral-charcoal">
              What would you like to receive?
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              {Object.entries(formData.preferences).map(([key, value]) => (
                <label
                  key={key}
                  className="flex items-center space-x-2 cursor-pointer"
                >
                  <input
                    type="checkbox"
                    checked={value}
                    onChange={() => handlePreferenceChange(key as keyof NewsletterFormData['preferences'])}
                    className="w-4 h-4 text-primary-sage border-neutral-stone rounded focus:ring-primary-sage"
                  />
                  <span className="text-sm text-neutral-charcoal capitalize">
                    {key === 'newProducts' ? 'New Products' : key}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Privacy Notice */}
        <p className="text-xs text-neutral-charcoal/60">
          By subscribing, you agree to our{' '}
          <a href="/privacy" className="text-primary-sage hover:underline">
            Privacy Policy
          </a>{' '}
          and consent to receive marketing emails. You can unsubscribe at any time.
        </p>
      </form>
    </div>
  );

  if (variant === 'card') {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          {formContent}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {formContent}
    </div>
  );
};

export default NewsletterForm;
